Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    main.o(i.main) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to peripheralinit.o(i.PeripheralInit) for PeripheralInit
    main.o(i.main) refers to usart.o(i.usart_init) for usart_init
    main.o(i.main) refers to lze_lcd.o(i.LCD_Clear) for LCD_Clear
    main.o(i.main) refers to lze_lcd.o(i.LCD_DisplayString) for LCD_DisplayString
    main.o(i.main) refers to ad7606b.o(i.AD7606B_Init) for AD7606B_Init
    main.o(i.main) refers to ad7606b.o(i.AD7606B_Set_Range) for AD7606B_Set_Range
    main.o(i.main) refers to ad7606b.o(i.AD7606B_Reset) for AD7606B_Reset
    main.o(i.main) refers to ad7606b.o(i.AD7606B_Conversion) for AD7606B_Conversion
    main.o(i.main) refers to ad7606b.o(i.AD7606B_Read_Data) for AD7606B_Read_Data
    main.o(i.main) refers to ad7606b.o(i.AD7606B_Digital2Voltage) for AD7606B_Digital2Voltage
    main.o(i.main) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to delay.o(i.Delay_50ms) for Delay_50ms
    main.o(i.main) refers to ad7606b.o(.bss) for ad7606b_data
    peripheralinit.o(i.PeripheralInit) refers to lze_lcd.o(i.LCD_Init) for LCD_Init
    lze_lcd.o(i.GetChineseCode) refers to lze_lcd.o(.data) for LCD_Currentfonts_CN
    lze_lcd.o(i.LCD_Clear) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_Clear) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_Clear) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    lze_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    lze_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    lze_lcd.o(i.LCD_DisplayChar) refers to lze_lcd.o(i.LCD_DrawChar) for LCD_DrawChar
    lze_lcd.o(i.LCD_DisplayChar) refers to lze_lcd.o(.data) for LCD_Currentfonts
    lze_lcd.o(i.LCD_DisplayChineseChar) refers to lze_lcd.o(i.GetChineseCode) for GetChineseCode
    lze_lcd.o(i.LCD_DisplayChineseChar) refers to lze_lcd.o(i.LCD_DrawChineseChar) for LCD_DrawChineseChar
    lze_lcd.o(i.LCD_DisplayOff) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_DisplayOff) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_DisplayOff) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DisplayOn) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_DisplayOn) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_DisplayOn) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DisplayString) refers to lze_lcd.o(i.LCD_DisplayChar) for LCD_DisplayChar
    lze_lcd.o(i.LCD_DisplayString) refers to lze_lcd.o(i.LCD_DisplayChineseChar) for LCD_DisplayChineseChar
    lze_lcd.o(i.LCD_DisplayString) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DisplayString) refers to lze_lcd.o(.data) for LCD_Currentfonts
    lze_lcd.o(i.LCD_Display_Dir) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DotLine_H) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_DotLine_H) refers to lze_lcd.o(i.LCD_DrawDotHLine) for LCD_DrawDotHLine
    lze_lcd.o(i.LCD_DotLine_V) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_DotLine_V) refers to lze_lcd.o(i.LCD_DrawDotVLine) for LCD_DrawDotVLine
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(.data) for LCD_Currentfonts
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(.data) for LCD_Currentfonts_CN
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawRect) refers to lze_lcd.o(i.LCD_DrawHLine) for LCD_DrawHLine
    lze_lcd.o(i.LCD_DrawRect) refers to lze_lcd.o(i.LCD_DrawVLine) for LCD_DrawVLine
    lze_lcd.o(i.LCD_DrawRgbPict) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawRgbPict) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawRgbPict) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawRgbPict) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_FSMCConfig) refers to stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    lze_lcd.o(i.LCD_FSMCConfig) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    lze_lcd.o(i.LCD_FSMCConfig) refers to stm32f10x_fsmc.o(i.FSMC_NORSRAMInit) for FSMC_NORSRAMInit
    lze_lcd.o(i.LCD_FSMCConfig) refers to stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd) for FSMC_NORSRAMCmd
    lze_lcd.o(i.LCD_FillBox) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_FillBox) refers to lze_lcd.o(i.LCD_DrawFullRect) for LCD_DrawFullRect
    lze_lcd.o(i.LCD_FillBox) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_GetColors) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_CtrlLinesConfig) for LCD_CtrlLinesConfig
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_FSMCConfig) for LCD_FSMCConfig
    lze_lcd.o(i.LCD_Init) refers to delay.o(i.Delay_1ms) for Delay_1ms
    lze_lcd.o(i.LCD_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    lze_lcd.o(i.LCD_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_ReadReg) for LCD_ReadReg
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_ReadRAM) for LCD_ReadRAM
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_SetFont) for LCD_SetFont
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(.data) for Font16_CN
    lze_lcd.o(i.LCD_LineBox) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_LineBox) refers to lze_lcd.o(i.LCD_DrawRect) for LCD_DrawRect
    lze_lcd.o(i.LCD_LineBox) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_Line_H) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_Line_H) refers to lze_lcd.o(i.LCD_DrawHLine) for LCD_DrawHLine
    lze_lcd.o(i.LCD_Line_H) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_Line_V) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_Line_V) refers to lze_lcd.o(i.LCD_DrawVLine) for LCD_DrawVLine
    lze_lcd.o(i.LCD_Line_V) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_PowerOn) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_PowerOn) refers to delay.o(i.Delay_1ms) for Delay_1ms
    lze_lcd.o(i.LCD_PowerOn) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_SetBackColor) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_SetColors) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_SetCursor) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_SetCursor) refers to lze_lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lze_lcd.o(i.LCD_SetCursor) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_SetCursor) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_SetFont) refers to lze_lcd.o(.data) for LCD_Currentfonts
    lze_lcd.o(i.LCD_SetPoint) refers to lze_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lze_lcd.o(i.LCD_SetPoint) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_SetPoint) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_SetPoint) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_SetTextColor) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_WindowModeDisable) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_WindowModeDisable) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_WriteChinese24x24string) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_WriteChinese24x24string) refers to lze_lcd.o(i.LCD_SetFont) for LCD_SetFont
    lze_lcd.o(i.LCD_WriteChinese24x24string) refers to lze_lcd.o(i.LCD_DisplayString) for LCD_DisplayString
    lze_lcd.o(i.LCD_WriteChinese24x24string) refers to lze_lcd.o(.data) for Font24_CN
    lze_lcd.o(i.LCD_WriteNumChar) refers to printfa.o(i.__0sprintf) for __2sprintf
    lze_lcd.o(i.LCD_WriteNumChar) refers to lze_lcd.o(i.LCD_WriteString) for LCD_WriteString
    lze_lcd.o(i.LCD_WriteNumInt) refers to printfa.o(i.__0sprintf) for __2sprintf
    lze_lcd.o(i.LCD_WriteNumInt) refers to lze_lcd.o(i.LCD_WriteString) for LCD_WriteString
    lze_lcd.o(i.LCD_WriteNumLong) refers to printfa.o(i.__0sprintf) for __2sprintf
    lze_lcd.o(i.LCD_WriteNumLong) refers to lze_lcd.o(i.LCD_WriteString) for LCD_WriteString
    lze_lcd.o(i.LCD_WriteRAM_Prepare) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_WriteString) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_WriteString) refers to lze_lcd.o(i.LCD_DisplayString) for LCD_DisplayString
    lze_lcd.o(i.PutPixel) refers to lze_lcd.o(i.LCD_DrawHLine) for LCD_DrawHLine
    lze_lcd.o(i.PutPixel) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(.data) refers to lze_lcd.o(.constdata) for Font8_Table
    ad7606b.o(i.AD7606B_Conversion) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ad7606b.o(i.AD7606B_Conversion) refers to delay.o(i.Delay_1ms) for Delay_1ms
    ad7606b.o(i.AD7606B_Digital2Voltage) refers to dfltui.o(.text) for __aeabi_ui2d
    ad7606b.o(i.AD7606B_Digital2Voltage) refers to dmul.o(.text) for __aeabi_dmul
    ad7606b.o(i.AD7606B_Digital2Voltage) refers to ddiv.o(.text) for __aeabi_ddiv
    ad7606b.o(i.AD7606B_Digital2Voltage) refers to ad7606b.o(.data) for ad7606b_range
    ad7606b.o(i.AD7606B_Get_Pin_Data) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ad7606b.o(i.AD7606B_Get_Pin_Data) refers to ad7606b.o(.data) for pin_list
    ad7606b.o(i.AD7606B_Init) refers to gpio.o(i.AD7606B_Parallel_GPIO_Init) for AD7606B_Parallel_GPIO_Init
    ad7606b.o(i.AD7606B_Init) refers to ad7606b.o(i.AD7606B_Set_DBx_Pin_Input_Mode) for AD7606B_Set_DBx_Pin_Input_Mode
    ad7606b.o(i.AD7606B_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ad7606b.o(i.AD7606B_Init) refers to ad7606b.o(i.AD7606B_Working_Mode) for AD7606B_Working_Mode
    ad7606b.o(i.AD7606B_Read_Data) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ad7606b.o(i.AD7606B_Read_Data) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ad7606b.o(i.AD7606B_Read_Data) refers to ad7606b.o(i.AD7606B_Get_Pin_Data) for AD7606B_Get_Pin_Data
    ad7606b.o(i.AD7606B_Reset) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ad7606b.o(i.AD7606B_Reset) refers to delay.o(i.Delay_1ms) for Delay_1ms
    ad7606b.o(i.AD7606B_Set_Address_Pin) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ad7606b.o(i.AD7606B_Set_Address_Pin) refers to ad7606b.o(.data) for pin_list
    ad7606b.o(i.AD7606B_Set_DBx_Pin_Input_Mode) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ad7606b.o(i.AD7606B_Set_DBx_Pin_Input_Mode) refers to ad7606b.o(.data) for pin_list
    ad7606b.o(i.AD7606B_Set_DBx_Pin_Output_Mode) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ad7606b.o(i.AD7606B_Set_DBx_Pin_Output_Mode) refers to ad7606b.o(.data) for pin_list
    ad7606b.o(i.AD7606B_Set_Range) refers to ad7606b.o(.data) for ad7606b_range
    ad7606b.o(i.AD7606B_Start) refers to ad7606b.o(i.AD7606B_Write_Register) for AD7606B_Write_Register
    ad7606b.o(i.AD7606B_Working_Mode) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ad7606b.o(i.AD7606B_Write_Register) refers to ad7606b.o(i.AD7606B_Set_DBx_Pin_Output_Mode) for AD7606B_Set_DBx_Pin_Output_Mode
    ad7606b.o(i.AD7606B_Write_Register) refers to delay.o(i.Delay_1ms) for Delay_1ms
    ad7606b.o(i.AD7606B_Write_Register) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ad7606b.o(i.AD7606B_Write_Register) refers to ad7606b.o(i.AD7606B_Set_Address_Pin) for AD7606B_Set_Address_Pin
    ad7606b.o(i.AD7606B_Write_Register) refers to ad7606b.o(i.AD7606_Set_Data_Pin) for AD7606_Set_Data_Pin
    ad7606b.o(i.AD7606B_Write_Register) refers to ad7606b.o(i.AD7606B_Set_DBx_Pin_Input_Mode) for AD7606B_Set_DBx_Pin_Input_Mode
    ad7606b.o(i.AD7606_Set_Data_Pin) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    ad7606b.o(i.AD7606_Set_Data_Pin) refers to ad7606b.o(.data) for pin_list
    gpio.o(i.AD7606B_Parallel_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    gpio.o(i.AD7606B_Parallel_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    gpio.o(i.AD7606B_Parallel_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    gpio.o(i.AD7606B_Serial_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    gpio.o(i.AD7606B_Serial_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    gpio.o(i.AD7606B_Serial_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.usart_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.usart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.data), (5000 bytes).
    Removing delay.o(i.Delay), (18 bytes).
    Removing delay.o(i.Delay_10us), (36 bytes).
    Removing delay.o(i.Delay_1us), (26 bytes).
    Removing delay.o(i.Delay_250us), (36 bytes).
    Removing delay.o(i.Delay_2us), (26 bytes).
    Removing delay.o(i.Delay_5ms), (38 bytes).
    Removing delay.o(i.Delay_882us), (26 bytes).
    Removing delay.o(i.Delay_ns), (12 bytes).
    Removing delay.o(i.TimingDelay_Decrement), (2 bytes).
    Removing lze_lcd.o(i.LCD_DisplayOff), (36 bytes).
    Removing lze_lcd.o(i.LCD_DisplayOn), (40 bytes).
    Removing lze_lcd.o(i.LCD_DotLine_H), (38 bytes).
    Removing lze_lcd.o(i.LCD_DotLine_V), (38 bytes).
    Removing lze_lcd.o(i.LCD_DrawDotHLine), (100 bytes).
    Removing lze_lcd.o(i.LCD_DrawDotVLine), (100 bytes).
    Removing lze_lcd.o(i.LCD_DrawFullRect), (80 bytes).
    Removing lze_lcd.o(i.LCD_DrawHLine), (72 bytes).
    Removing lze_lcd.o(i.LCD_DrawMonoPict), (124 bytes).
    Removing lze_lcd.o(i.LCD_DrawRect), (64 bytes).
    Removing lze_lcd.o(i.LCD_DrawRgbPict), (92 bytes).
    Removing lze_lcd.o(i.LCD_DrawVLine), (72 bytes).
    Removing lze_lcd.o(i.LCD_FillBox), (48 bytes).
    Removing lze_lcd.o(i.LCD_GetColors), (24 bytes).
    Removing lze_lcd.o(i.LCD_LineBox), (48 bytes).
    Removing lze_lcd.o(i.LCD_Line_H), (40 bytes).
    Removing lze_lcd.o(i.LCD_Line_V), (40 bytes).
    Removing lze_lcd.o(i.LCD_PowerOn), (136 bytes).
    Removing lze_lcd.o(i.LCD_SetBackColor), (16 bytes).
    Removing lze_lcd.o(i.LCD_SetColors), (28 bytes).
    Removing lze_lcd.o(i.LCD_SetPoint), (136 bytes).
    Removing lze_lcd.o(i.LCD_SetTextColor), (16 bytes).
    Removing lze_lcd.o(i.LCD_WindowModeDisable), (28 bytes).
    Removing lze_lcd.o(i.LCD_WriteChinese24x24string), (68 bytes).
    Removing lze_lcd.o(i.LCD_WriteNumChar), (52 bytes).
    Removing lze_lcd.o(i.LCD_WriteNumInt), (52 bytes).
    Removing lze_lcd.o(i.LCD_WriteNumLong), (56 bytes).
    Removing lze_lcd.o(i.LCD_WriteString), (38 bytes).
    Removing lze_lcd.o(i.PutPixel), (56 bytes).
    Removing ad7606b.o(i.AD7606B_Set_Address_Pin), (104 bytes).
    Removing ad7606b.o(i.AD7606B_Set_DBx_Pin_Output_Mode), (64 bytes).
    Removing ad7606b.o(i.AD7606B_Start), (12 bytes).
    Removing ad7606b.o(i.AD7606B_Write_Register), (88 bytes).
    Removing ad7606b.o(i.AD7606_Set_Data_Pin), (80 bytes).
    Removing gpio.o(i.AD7606B_Serial_GPIO_Init), (128 bytes).
    Removing misc.o(i.NVIC_Init), (112 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).

138 unused section(s) (total 11816 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CMSIS\CoreSupport\core_cm3.c          0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\User\Delay.c                          0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\PeripheralInit.c                 0x00000000   Number         0  peripheralinit.o ABSOLUTE
    ..\User\ad7606b.c                        0x00000000   Number         0  ad7606b.o ABSOLUTE
    ..\User\gpio.c                           0x00000000   Number         0  gpio.o ABSOLUTE
    ..\User\lze_lcd.c                        0x00000000   Number         0  lze_lcd.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\usart.c                          0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CMSIS\\CoreSupport\\core_cm3.c       0x00000000   Number         0  core_cm3.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000168   Section        0  memseta.o(.text)
    .text                                    0x0800018c   Section        0  dmul.o(.text)
    .text                                    0x08000270   Section        0  ddiv.o(.text)
    .text                                    0x0800034e   Section        0  dfltui.o(.text)
    .text                                    0x08000368   Section        0  uidiv.o(.text)
    .text                                    0x08000394   Section        0  uldiv.o(.text)
    .text                                    0x080003f6   Section        0  iusefp.o(.text)
    .text                                    0x080003f6   Section        0  depilogue.o(.text)
    .text                                    0x080004b0   Section        0  dadd.o(.text)
    .text                                    0x080005fe   Section        0  dfixul.o(.text)
    .text                                    0x08000630   Section       48  cdrcmple.o(.text)
    .text                                    0x08000660   Section       36  init.o(.text)
    .text                                    0x08000684   Section        0  llshl.o(.text)
    .text                                    0x080006a2   Section        0  llushr.o(.text)
    .text                                    0x080006c2   Section        0  llsshr.o(.text)
    i.AD7606B_Conversion                     0x080006e8   Section        0  ad7606b.o(i.AD7606B_Conversion)
    i.AD7606B_Digital2Voltage                0x08000710   Section        0  ad7606b.o(i.AD7606B_Digital2Voltage)
    i.AD7606B_Get_Pin_Data                   0x080007b8   Section        0  ad7606b.o(i.AD7606B_Get_Pin_Data)
    AD7606B_Get_Pin_Data                     0x080007b9   Thumb Code    64  ad7606b.o(i.AD7606B_Get_Pin_Data)
    i.AD7606B_Init                           0x08000800   Section        0  ad7606b.o(i.AD7606B_Init)
    i.AD7606B_Parallel_GPIO_Init             0x08000844   Section        0  gpio.o(i.AD7606B_Parallel_GPIO_Init)
    i.AD7606B_Read_Data                      0x080008fc   Section        0  ad7606b.o(i.AD7606B_Read_Data)
    i.AD7606B_Reset                          0x08000954   Section        0  ad7606b.o(i.AD7606B_Reset)
    i.AD7606B_Set_DBx_Pin_Input_Mode         0x08000988   Section        0  ad7606b.o(i.AD7606B_Set_DBx_Pin_Input_Mode)
    AD7606B_Set_DBx_Pin_Input_Mode           0x08000989   Thumb Code    54  ad7606b.o(i.AD7606B_Set_DBx_Pin_Input_Mode)
    i.AD7606B_Set_Range                      0x080009c8   Section        0  ad7606b.o(i.AD7606B_Set_Range)
    i.AD7606B_Working_Mode                   0x080009d4   Section        0  ad7606b.o(i.AD7606B_Working_Mode)
    i.Delay_1ms                              0x08000a24   Section        0  delay.o(i.Delay_1ms)
    i.Delay_50ms                             0x08000a48   Section        0  delay.o(i.Delay_50ms)
    i.FSMC_NORSRAMCmd                        0x08000a70   Section        0  stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd)
    i.FSMC_NORSRAMInit                       0x08000aa4   Section        0  stm32f10x_fsmc.o(i.FSMC_NORSRAMInit)
    i.GPIO_Init                              0x08000b8a   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x08000ca0   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000cb2   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000cb6   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x08000cba   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.GetChineseCode                         0x08000cc4   Section        0  lze_lcd.o(i.GetChineseCode)
    i.LCD_Clear                              0x08000d20   Section        0  lze_lcd.o(i.LCD_Clear)
    i.LCD_CtrlLinesConfig                    0x08000d5c   Section        0  lze_lcd.o(i.LCD_CtrlLinesConfig)
    i.LCD_DisplayChar                        0x08000dd0   Section        0  lze_lcd.o(i.LCD_DisplayChar)
    i.LCD_DisplayChineseChar                 0x08000e14   Section        0  lze_lcd.o(i.LCD_DisplayChineseChar)
    i.LCD_DisplayString                      0x08000e38   Section        0  lze_lcd.o(i.LCD_DisplayString)
    i.LCD_Display_Dir                        0x08000eb4   Section        0  lze_lcd.o(i.LCD_Display_Dir)
    i.LCD_DrawChar                           0x08001000   Section        0  lze_lcd.o(i.LCD_DrawChar)
    LCD_DrawChar                             0x08001001   Thumb Code   140  lze_lcd.o(i.LCD_DrawChar)
    i.LCD_DrawChineseChar                    0x0800109c   Section        0  lze_lcd.o(i.LCD_DrawChineseChar)
    LCD_DrawChineseChar                      0x0800109d   Thumb Code   164  lze_lcd.o(i.LCD_DrawChineseChar)
    i.LCD_FSMCConfig                         0x08001150   Section        0  lze_lcd.o(i.LCD_FSMCConfig)
    i.LCD_Init                               0x080011bc   Section        0  lze_lcd.o(i.LCD_Init)
    i.LCD_ReadRAM                            0x08001650   Section        0  lze_lcd.o(i.LCD_ReadRAM)
    i.LCD_ReadReg                            0x0800165c   Section        0  lze_lcd.o(i.LCD_ReadReg)
    i.LCD_SetCursor                          0x0800166c   Section        0  lze_lcd.o(i.LCD_SetCursor)
    LCD_SetCursor                            0x0800166d   Thumb Code   100  lze_lcd.o(i.LCD_SetCursor)
    i.LCD_SetDisplayWindow                   0x080016d4   Section        0  lze_lcd.o(i.LCD_SetDisplayWindow)
    i.LCD_SetFont                            0x08001864   Section        0  lze_lcd.o(i.LCD_SetFont)
    i.LCD_WR_DATA                            0x08001878   Section        0  lze_lcd.o(i.LCD_WR_DATA)
    i.LCD_WR_REG                             0x08001884   Section        0  lze_lcd.o(i.LCD_WR_REG)
    i.LCD_WriteRAM                           0x08001890   Section        0  lze_lcd.o(i.LCD_WriteRAM)
    i.LCD_WriteRAM_Prepare                   0x0800189c   Section        0  lze_lcd.o(i.LCD_WriteRAM_Prepare)
    i.LCD_WriteReg                           0x080018b0   Section        0  lze_lcd.o(i.LCD_WriteReg)
    i.PeripheralInit                         0x080018c0   Section        0  peripheralinit.o(i.PeripheralInit)
    i.RCC_AHBPeriphClockCmd                  0x080018c8   Section        0  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080018e8   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08001908   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SetSysClock                            0x080019dc   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x080019dd   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x080019e4   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x080019e5   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SystemInit                             0x08001ac4   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART_Cmd                              0x08001b24   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001b3c   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_Init                             0x08001b58   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_SendData                         0x08001c30   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.__0printf                              0x08001c38   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x08001c58   Section        0  printfa.o(i.__0sprintf)
    i.__scatterload_copy                     0x08001c80   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08001c8e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001c90   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08001ca0   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08001ca1   Thumb Code   334  printfa.o(i._fp_digits)
    i._printf_core                           0x08001e04   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08001e05   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x080024e0   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x080024e1   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002504   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002505   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08002532   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08002533   Thumb Code    10  printfa.o(i._sputc)
    i.fputc                                  0x0800253c   Section        0  usart.o(i.fputc)
    i.main                                   0x08002560   Section        0  main.o(i.main)
    i.usart_init                             0x08002640   Section        0  usart.o(i.usart_init)
    .constdata                               0x080026d0   Section    16270  lze_lcd.o(.constdata)
    .data                                    0x20000000   Section       76  lze_lcd.o(.data)
    .data                                    0x2000004c   Section      100  ad7606b.o(.data)
    .data                                    0x200000b0   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x200000b0   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x200000c0   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x200000c4   Section        4  stdout.o(.data)
    .bss                                     0x200000c8   Section       16  lze_lcd.o(.bss)
    .bss                                     0x200000d8   Section       16  ad7606b.o(.bss)
    STACK                                    0x200000e8   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x0800014d   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x0800014f   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x08000151   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x08000153   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x08000155   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x08000157   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x08000159   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x0800015b   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x0800015d   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_memset                           0x08000169   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000169   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000169   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000177   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000177   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000177   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800017b   Thumb Code    18  memseta.o(.text)
    __aeabi_dmul                             0x0800018d   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000271   Thumb Code   222  ddiv.o(.text)
    __aeabi_ui2d                             0x0800034f   Thumb Code    26  dfltui.o(.text)
    __aeabi_uidiv                            0x08000369   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000369   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000395   Thumb Code    98  uldiv.o(.text)
    __I$use$fp                               0x080003f7   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x080003f7   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000415   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x080004b1   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080005f3   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080005f9   Thumb Code     6  dadd.o(.text)
    __aeabi_d2ulz                            0x080005ff   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000631   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000661   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000661   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000685   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000685   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080006a3   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080006a3   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080006c3   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006c3   Thumb Code     0  llsshr.o(.text)
    AD7606B_Conversion                       0x080006e9   Thumb Code    36  ad7606b.o(i.AD7606B_Conversion)
    AD7606B_Digital2Voltage                  0x08000711   Thumb Code   144  ad7606b.o(i.AD7606B_Digital2Voltage)
    AD7606B_Init                             0x08000801   Thumb Code    60  ad7606b.o(i.AD7606B_Init)
    AD7606B_Parallel_GPIO_Init               0x08000845   Thumb Code   172  gpio.o(i.AD7606B_Parallel_GPIO_Init)
    AD7606B_Read_Data                        0x080008fd   Thumb Code    84  ad7606b.o(i.AD7606B_Read_Data)
    AD7606B_Reset                            0x08000955   Thumb Code    46  ad7606b.o(i.AD7606B_Reset)
    AD7606B_Set_Range                        0x080009c9   Thumb Code     6  ad7606b.o(i.AD7606B_Set_Range)
    AD7606B_Working_Mode                     0x080009d5   Thumb Code    74  ad7606b.o(i.AD7606B_Working_Mode)
    Delay_1ms                                0x08000a25   Thumb Code    36  delay.o(i.Delay_1ms)
    Delay_50ms                               0x08000a49   Thumb Code    40  delay.o(i.Delay_50ms)
    FSMC_NORSRAMCmd                          0x08000a71   Thumb Code    46  stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd)
    FSMC_NORSRAMInit                         0x08000aa5   Thumb Code   230  stm32f10x_fsmc.o(i.FSMC_NORSRAMInit)
    GPIO_Init                                0x08000b8b   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08000ca1   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000cb3   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000cb7   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x08000cbb   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    GetChineseCode                           0x08000cc5   Thumb Code    88  lze_lcd.o(i.GetChineseCode)
    LCD_Clear                                0x08000d21   Thumb Code    50  lze_lcd.o(i.LCD_Clear)
    LCD_CtrlLinesConfig                      0x08000d5d   Thumb Code   106  lze_lcd.o(i.LCD_CtrlLinesConfig)
    LCD_DisplayChar                          0x08000dd1   Thumb Code    62  lze_lcd.o(i.LCD_DisplayChar)
    LCD_DisplayChineseChar                   0x08000e15   Thumb Code    34  lze_lcd.o(i.LCD_DisplayChineseChar)
    LCD_DisplayString                        0x08000e39   Thumb Code   110  lze_lcd.o(i.LCD_DisplayString)
    LCD_Display_Dir                          0x08000eb5   Thumb Code   328  lze_lcd.o(i.LCD_Display_Dir)
    LCD_FSMCConfig                           0x08001151   Thumb Code   108  lze_lcd.o(i.LCD_FSMCConfig)
    LCD_Init                                 0x080011bd   Thumb Code  1154  lze_lcd.o(i.LCD_Init)
    LCD_ReadRAM                              0x08001651   Thumb Code     6  lze_lcd.o(i.LCD_ReadRAM)
    LCD_ReadReg                              0x0800165d   Thumb Code    12  lze_lcd.o(i.LCD_ReadReg)
    LCD_SetDisplayWindow                     0x080016d5   Thumb Code   396  lze_lcd.o(i.LCD_SetDisplayWindow)
    LCD_SetFont                              0x08001865   Thumb Code    10  lze_lcd.o(i.LCD_SetFont)
    LCD_WR_DATA                              0x08001879   Thumb Code     6  lze_lcd.o(i.LCD_WR_DATA)
    LCD_WR_REG                               0x08001885   Thumb Code     6  lze_lcd.o(i.LCD_WR_REG)
    LCD_WriteRAM                             0x08001891   Thumb Code     6  lze_lcd.o(i.LCD_WriteRAM)
    LCD_WriteRAM_Prepare                     0x0800189d   Thumb Code    10  lze_lcd.o(i.LCD_WriteRAM_Prepare)
    LCD_WriteReg                             0x080018b1   Thumb Code    10  lze_lcd.o(i.LCD_WriteReg)
    PeripheralInit                           0x080018c1   Thumb Code     8  peripheralinit.o(i.PeripheralInit)
    RCC_AHBPeriphClockCmd                    0x080018c9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080018e9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001909   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SystemInit                               0x08001ac5   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART_Cmd                                0x08001b25   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001b3d   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_Init                               0x08001b59   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_SendData                           0x08001c31   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    __0printf                                0x08001c39   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08001c39   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08001c39   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08001c39   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08001c39   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x08001c59   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08001c59   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08001c59   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08001c59   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08001c59   Thumb Code     0  printfa.o(i.__0sprintf)
    __scatterload_copy                       0x08001c81   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08001c8f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001c91   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fputc                                    0x0800253d   Thumb Code    32  usart.o(i.fputc)
    main                                     0x08002561   Thumb Code   152  main.o(i.main)
    usart_init                               0x08002641   Thumb Code   134  usart.o(i.usart_init)
    Font8_Table                              0x080026d0   Data         760  lze_lcd.o(.constdata)
    Font12_Table                             0x080029c8   Data        1140  lze_lcd.o(.constdata)
    Font16_Table                             0x08002e3c   Data        1520  lze_lcd.o(.constdata)
    Font20_Table                             0x0800342c   Data        3800  lze_lcd.o(.constdata)
    Font24_Table                             0x08004304   Data        6840  lze_lcd.o(.constdata)
    codeGB_16                                0x08005dbc   Data        1690  lze_lcd.o(.constdata)
    codeGB_24                                0x08006456   Data         520  lze_lcd.o(.constdata)
    Region$$Table$$Base                      0x08006660   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006680   Number         0  anon$$obj.o(Region$$Table)
    Font8                                    0x20000000   Data           8  lze_lcd.o(.data)
    Font12                                   0x20000008   Data           8  lze_lcd.o(.data)
    Font16                                   0x20000010   Data           8  lze_lcd.o(.data)
    Font20                                   0x20000018   Data           8  lze_lcd.o(.data)
    Font24                                   0x20000020   Data           8  lze_lcd.o(.data)
    Font16_CN                                0x20000028   Data          12  lze_lcd.o(.data)
    Font24_CN                                0x20000034   Data          12  lze_lcd.o(.data)
    LCD_Currentfonts                         0x20000040   Data           4  lze_lcd.o(.data)
    LCD_Currentfonts_CN                      0x20000044   Data           4  lze_lcd.o(.data)
    TextColor                                0x20000048   Data           2  lze_lcd.o(.data)
    BackColor                                0x2000004a   Data           2  lze_lcd.o(.data)
    ad7606b_range                            0x2000004c   Data           1  ad7606b.o(.data)
    port_list                                0x20000050   Data          64  ad7606b.o(.data)
    pin_list                                 0x20000090   Data          32  ad7606b.o(.data)
    __stdout                                 0x200000c4   Data           4  stdout.o(.data)
    LCD_Dev                                  0x200000c8   Data          16  lze_lcd.o(.bss)
    ad7606b_data                             0x200000d8   Data          16  ad7606b.o(.bss)
    __initial_sp                             0x200004e8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006748, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006680, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO         1328    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         1333  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         1605    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         1608    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1610    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1612    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         1613    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1615    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1617    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         1606    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO         1329    .text               startup_stm32f10x_hd.o
    0x08000168   0x08000168   0x00000024   Code   RO         1336    .text               mc_w.l(memseta.o)
    0x0800018c   0x0800018c   0x000000e4   Code   RO         1599    .text               mf_w.l(dmul.o)
    0x08000270   0x08000270   0x000000de   Code   RO         1601    .text               mf_w.l(ddiv.o)
    0x0800034e   0x0800034e   0x0000001a   Code   RO         1603    .text               mf_w.l(dfltui.o)
    0x08000368   0x08000368   0x0000002c   Code   RO         1620    .text               mc_w.l(uidiv.o)
    0x08000394   0x08000394   0x00000062   Code   RO         1622    .text               mc_w.l(uldiv.o)
    0x080003f6   0x080003f6   0x00000000   Code   RO         1624    .text               mc_w.l(iusefp.o)
    0x080003f6   0x080003f6   0x000000ba   Code   RO         1625    .text               mf_w.l(depilogue.o)
    0x080004b0   0x080004b0   0x0000014e   Code   RO         1627    .text               mf_w.l(dadd.o)
    0x080005fe   0x080005fe   0x00000030   Code   RO         1629    .text               mf_w.l(dfixul.o)
    0x0800062e   0x0800062e   0x00000002   PAD
    0x08000630   0x08000630   0x00000030   Code   RO         1631    .text               mf_w.l(cdrcmple.o)
    0x08000660   0x08000660   0x00000024   Code   RO         1633    .text               mc_w.l(init.o)
    0x08000684   0x08000684   0x0000001e   Code   RO         1635    .text               mc_w.l(llshl.o)
    0x080006a2   0x080006a2   0x00000020   Code   RO         1637    .text               mc_w.l(llushr.o)
    0x080006c2   0x080006c2   0x00000024   Code   RO         1639    .text               mc_w.l(llsshr.o)
    0x080006e6   0x080006e6   0x00000002   PAD
    0x080006e8   0x080006e8   0x00000028   Code   RO          501    i.AD7606B_Conversion  ad7606b.o
    0x08000710   0x08000710   0x000000a8   Code   RO          502    i.AD7606B_Digital2Voltage  ad7606b.o
    0x080007b8   0x080007b8   0x00000048   Code   RO          503    i.AD7606B_Get_Pin_Data  ad7606b.o
    0x08000800   0x08000800   0x00000044   Code   RO          504    i.AD7606B_Init      ad7606b.o
    0x08000844   0x08000844   0x000000b8   Code   RO          595    i.AD7606B_Parallel_GPIO_Init  gpio.o
    0x080008fc   0x080008fc   0x00000058   Code   RO          505    i.AD7606B_Read_Data  ad7606b.o
    0x08000954   0x08000954   0x00000034   Code   RO          506    i.AD7606B_Reset     ad7606b.o
    0x08000988   0x08000988   0x00000040   Code   RO          508    i.AD7606B_Set_DBx_Pin_Input_Mode  ad7606b.o
    0x080009c8   0x080009c8   0x0000000c   Code   RO          510    i.AD7606B_Set_Range  ad7606b.o
    0x080009d4   0x080009d4   0x00000050   Code   RO          512    i.AD7606B_Working_Mode  ad7606b.o
    0x08000a24   0x08000a24   0x00000024   Code   RO           96    i.Delay_1ms         delay.o
    0x08000a48   0x08000a48   0x00000028   Code   RO          100    i.Delay_50ms        delay.o
    0x08000a70   0x08000a70   0x00000034   Code   RO          992    i.FSMC_NORSRAMCmd   stm32f10x_fsmc.o
    0x08000aa4   0x08000aa4   0x000000e6   Code   RO          994    i.FSMC_NORSRAMInit  stm32f10x_fsmc.o
    0x08000b8a   0x08000b8a   0x00000116   Code   RO          673    i.GPIO_Init         stm32f10x_gpio.o
    0x08000ca0   0x08000ca0   0x00000012   Code   RO          677    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000cb2   0x08000cb2   0x00000004   Code   RO          680    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000cb6   0x08000cb6   0x00000004   Code   RO          681    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08000cba   0x08000cba   0x0000000a   Code   RO          684    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08000cc4   0x08000cc4   0x0000005c   Code   RO          166    i.GetChineseCode    lze_lcd.o
    0x08000d20   0x08000d20   0x0000003c   Code   RO          167    i.LCD_Clear         lze_lcd.o
    0x08000d5c   0x08000d5c   0x00000074   Code   RO          168    i.LCD_CtrlLinesConfig  lze_lcd.o
    0x08000dd0   0x08000dd0   0x00000044   Code   RO          169    i.LCD_DisplayChar   lze_lcd.o
    0x08000e14   0x08000e14   0x00000022   Code   RO          170    i.LCD_DisplayChineseChar  lze_lcd.o
    0x08000e36   0x08000e36   0x00000002   PAD
    0x08000e38   0x08000e38   0x0000007c   Code   RO          173    i.LCD_DisplayString  lze_lcd.o
    0x08000eb4   0x08000eb4   0x0000014c   Code   RO          174    i.LCD_Display_Dir   lze_lcd.o
    0x08001000   0x08001000   0x0000009c   Code   RO          177    i.LCD_DrawChar      lze_lcd.o
    0x0800109c   0x0800109c   0x000000b4   Code   RO          178    i.LCD_DrawChineseChar  lze_lcd.o
    0x08001150   0x08001150   0x0000006c   Code   RO          187    i.LCD_FSMCConfig    lze_lcd.o
    0x080011bc   0x080011bc   0x00000494   Code   RO          190    i.LCD_Init          lze_lcd.o
    0x08001650   0x08001650   0x0000000c   Code   RO          195    i.LCD_ReadRAM       lze_lcd.o
    0x0800165c   0x0800165c   0x00000010   Code   RO          196    i.LCD_ReadReg       lze_lcd.o
    0x0800166c   0x0800166c   0x00000068   Code   RO          199    i.LCD_SetCursor     lze_lcd.o
    0x080016d4   0x080016d4   0x00000190   Code   RO          200    i.LCD_SetDisplayWindow  lze_lcd.o
    0x08001864   0x08001864   0x00000014   Code   RO          201    i.LCD_SetFont       lze_lcd.o
    0x08001878   0x08001878   0x0000000c   Code   RO          204    i.LCD_WR_DATA       lze_lcd.o
    0x08001884   0x08001884   0x0000000c   Code   RO          205    i.LCD_WR_REG        lze_lcd.o
    0x08001890   0x08001890   0x0000000c   Code   RO          211    i.LCD_WriteRAM      lze_lcd.o
    0x0800189c   0x0800189c   0x00000014   Code   RO          212    i.LCD_WriteRAM_Prepare  lze_lcd.o
    0x080018b0   0x080018b0   0x00000010   Code   RO          213    i.LCD_WriteReg      lze_lcd.o
    0x080018c0   0x080018c0   0x00000008   Code   RO           79    i.PeripheralInit    peripheralinit.o
    0x080018c8   0x080018c8   0x00000020   Code   RO          782    i.RCC_AHBPeriphClockCmd  stm32f10x_rcc.o
    0x080018e8   0x080018e8   0x00000020   Code   RO          785    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001908   0x08001908   0x000000d4   Code   RO          793    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080019dc   0x080019dc   0x00000008   Code   RO         1292    i.SetSysClock       system_stm32f10x.o
    0x080019e4   0x080019e4   0x000000e0   Code   RO         1293    i.SetSysClockTo72   system_stm32f10x.o
    0x08001ac4   0x08001ac4   0x00000060   Code   RO         1295    i.SystemInit        system_stm32f10x.o
    0x08001b24   0x08001b24   0x00000018   Code   RO         1105    i.USART_Cmd         stm32f10x_usart.o
    0x08001b3c   0x08001b3c   0x0000001a   Code   RO         1108    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08001b56   0x08001b56   0x00000002   PAD
    0x08001b58   0x08001b58   0x000000d8   Code   RO         1112    i.USART_Init        stm32f10x_usart.o
    0x08001c30   0x08001c30   0x00000008   Code   RO         1122    i.USART_SendData    stm32f10x_usart.o
    0x08001c38   0x08001c38   0x00000020   Code   RO         1571    i.__0printf         mc_w.l(printfa.o)
    0x08001c58   0x08001c58   0x00000028   Code   RO         1573    i.__0sprintf        mc_w.l(printfa.o)
    0x08001c80   0x08001c80   0x0000000e   Code   RO         1643    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08001c8e   0x08001c8e   0x00000002   Code   RO         1644    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001c90   0x08001c90   0x0000000e   Code   RO         1645    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08001c9e   0x08001c9e   0x00000002   PAD
    0x08001ca0   0x08001ca0   0x00000164   Code   RO         1578    i._fp_digits        mc_w.l(printfa.o)
    0x08001e04   0x08001e04   0x000006dc   Code   RO         1579    i._printf_core      mc_w.l(printfa.o)
    0x080024e0   0x080024e0   0x00000024   Code   RO         1580    i._printf_post_padding  mc_w.l(printfa.o)
    0x08002504   0x08002504   0x0000002e   Code   RO         1581    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08002532   0x08002532   0x0000000a   Code   RO         1583    i._sputc            mc_w.l(printfa.o)
    0x0800253c   0x0800253c   0x00000024   Code   RO          613    i.fputc             usart.o
    0x08002560   0x08002560   0x000000e0   Code   RO            1    i.main              main.o
    0x08002640   0x08002640   0x00000090   Code   RO          614    i.usart_init        usart.o
    0x080026d0   0x080026d0   0x00003f8e   Data   RO          217    .constdata          lze_lcd.o
    0x0800665e   0x0800665e   0x00000002   PAD
    0x08006660   0x08006660   0x00000020   Data   RO         1641    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006680, Size: 0x000004e8, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08006680   0x0000004c   Data   RW          218    .data               lze_lcd.o
    0x2000004c   0x080066cc   0x00000064   Data   RW          516    .data               ad7606b.o
    0x200000b0   0x08006730   0x00000014   Data   RW          813    .data               stm32f10x_rcc.o
    0x200000c4   0x08006744   0x00000004   Data   RW         1619    .data               mc_w.l(stdout.o)
    0x200000c8        -       0x00000010   Zero   RW          216    .bss                lze_lcd.o
    0x200000d8        -       0x00000010   Zero   RW          515    .bss                ad7606b.o
    0x200000e8        -       0x00000400   Zero   RW         1326    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       644         76          0        100         16       5569   ad7606b.o
         0          0          0          0          0         32   core_cm3.o
        76          0          0          0          0        969   delay.o
       184         12          0          0          0        573   gpio.o
      3066        168      16270         76         16      21002   lze_lcd.o
       224         72          0          0          0     240735   main.o
         8          0          0          0          0        457   peripheralinit.o
        36          8        304          0       1024        844   startup_stm32f10x_hd.o
       282          6          0          0          0       1877   stm32f10x_fsmc.o
       314          0          0          0          0       4356   stm32f10x_gpio.o
       276         32          0         20          0       4525   stm32f10x_rcc.o
       274          6          0          0          0       4120   stm32f10x_usart.o
       328         28          0          0          0       2085   system_stm32f10x.o
       180         14          0          0          0       1129   usart.o

    ----------------------------------------------------------------------
      5896        <USER>      <GROUP>        196       1056     288273   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          2          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2276         96          0          0          0        596   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3736        <USER>          <GROUP>          4          0       1880   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2638        112          0          4          0       1148   mc_w.l
      1092          0          0          0          0        732   mf_w.l

    ----------------------------------------------------------------------
      3736        <USER>          <GROUP>          4          0       1880   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9632        534      16608        200       1056     286665   Grand Totals
      9632        534      16608        200       1056     286665   ELF Image Totals
      9632        534      16608        200          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                26240 (  25.63kB)
    Total RW  Size (RW Data + ZI Data)              1256 (   1.23kB)
    Total ROM Size (Code + RO Data + RW Data)      26440 (  25.82kB)

==============================================================================

