*** Creating Trace Output File '.\Obj\STM32_TFT.tra' Ok.
### Preparing for ADS-LD.
### Creating ADS-LD Command Line
### List of Objects: adding '".\obj\main.o"'
### List of Objects: adding '".\obj\peripheralinit.o"'
### List of Objects: adding '".\obj\delay.o"'
### List of Objects: adding '".\obj\lze_lcd.o"'
### List of Objects: adding '".\obj\misc.o"'
### List of Objects: adding '".\obj\stm32f10x_gpio.o"'
### List of Objects: adding '".\obj\stm32f10x_rcc.o"'
### List of Objects: adding '".\obj\stm32f10x_fsmc.o"'
### List of Objects: adding '".\obj\core_cm3.o"'
### List of Objects: adding '".\obj\system_stm32f10x.o"'
### List of Objects: adding '".\obj\startup_stm32f10x_hd.o"'
### ADS-LD Command completed:
--cpu Cortex-M3 ".\obj\main.o" ".\obj\peripheralinit.o" ".\obj\delay.o" ".\obj\lze_lcd.o" ".\obj\misc.o" ".\obj\stm32f10x_gpio.o" ".\obj\stm32f10x_rcc.o" ".\obj\stm32f10x_fsmc.o" ".\obj\core_cm3.o" ".\obj\system_stm32f10x.o" ".\obj\startup_stm32f10x_hd.o" 

--library_type=microlib --strict --scatter ".\Obj\STM32_TFT.sct" 

--summary_stderr --info summarysizes --map --xref --callgraph --symbols 

--info sizes --info totals --info unused --info veneers 

 --list ".\List\STM32_TFT.map" -o .\Obj\STM32_TFT.axf### Preparing Environment (PrepEnvAds)
### ADS-LD Output File: '.\Obj\STM32_TFT.axf'
### ADS-LD Command File: '.\Obj\STM32_TFT.lnp'
### Checking for dirty Components...
### Creating CmdFile '.\Obj\STM32_TFT.lnp', Handle=0x000007C4
### Writing '.lnp' file
### ADS-LD Command file '.\Obj\STM32_TFT.lnp' is ready.
### ADS-LD: About to start ADS-LD Thread.
### ADS-LD: executed with 0 errors
### Updating obj list
### LDADS_file() completed.
