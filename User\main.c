//-----------------------------------------------------------------
// 程序描述:
//     AD7606B并行采集实验
// 作    者: 凌智电子
// 开始日期: 2024-03-25
// 完成日期: 2024-03-25
// 修改日期:
// 当前版本: V1.0
// 历史版本:
//  - V1.0: (2024-03-25)	AD7606B采集实验
// 调试工具: 凌智STM32F103核心板、LZE_ST_LINK2、4.3寸/2.8寸 TFT液晶屏
// 说    明:
//
//-----------------------------------------------------------------
// 接线说明
// AD7606B  ->  STM32F103
// OS0	  ->  	PC0
// OS1	  ->  	PC1
// OS2	  ->  	PC2
// SER	  ->  	PC3
// STBY	  ->  	PC4
// CONVSET	  ->  	PC5
// WR	  ->  	PC6
// RESET	  ->  	PC7
// RD	  ->  	PA0
// CS	  ->  	PA1
// BUSY	->	PA2
// FRSTDATA	->	PA3
// DB0	  ->    	PA4
// DB1	  ->    	PA5
// DB2	  ->    	PA6
// DB3	  ->    	PA7
// DB4	  ->    	PB8
// DB5	  ->    	PB9
// DB6	  ->    	PC14
// DB7	  ->    	PC15
// DB8	  ->    	PC12
// DB9	  ->    	PC13
// DB10	  ->    	PC10
// DB11	  ->    	PC11
// DB12	  ->    	PC8
// DB13	  ->    	PC9
// DB14	  ->    	PA14
// DB15	  ->    	PA15
//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#include "Delay.h"
#include "PeripheralInit.h"
#include "ad7606b.h"
#include "bmp.h"
#include "lze_lcd.h"
#include "usart.h"
#include <stdio.h>
#include <stm32f10x.h>

//-----------------------------------------------------------------
// 主程序
//-----------------------------------------------------------------
int main(void) {
  char led_buf[512] = {0};

  PeripheralInit(); // 外设初始化
  usart_init();
  LCD_Clear(LCD_COLOR_BLACK); // 设置背景色
  LCD_DisplayString(0, 0, "STM32 AD7606B Channels Test:");
#if USE_SOFTWARE_MODE
  AD7606B_Init(Software_Mode);
  AD7606B_Reset();
  AD7606B_Set_Range(Range_10_V);
#if USE_PARALLEL_MODE
#else
  AD7606_Set_Serial_Output_Format(_1Dout);
#endif
  AD7606B_Start();
#else
  AD7606B_Init(Hardware_Mode);
  AD7606B_Set_Range(Range_10_V);
  AD7606B_Reset();
#endif

  while (1) {
    AD7606B_Conversion();
    AD7606B_Read_Data(ad7606b_data);
    for (uint8_t i = 0; i < AD7606B_CHANNEL_MAX; i++) {
      sprintf(led_buf, "ch%d: %.1f mv 0x%x %ld  ", i + 1,
              AD7606B_Digital2Voltage(ad7606b_data[i]), ad7606b_data[i],
              ad7606b_data[i]);
      LCD_DisplayString(0, 32 + 16 * i, (uint8_t *)led_buf);
      printf("%s\r\n", led_buf);
    }
    Delay_50ms(10);
  }
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
