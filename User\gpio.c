/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file    gpio.c
 * @brief   This file provides code for the configuration
 *          of all used GPIO pins.
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2024 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "gpio.h"

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure GPIO                                                             */
/*----------------------------------------------------------------------------*/
/* USER CODE BEGIN 1 */

/* USER CODE END 1 */

/* USER CODE BEGIN 2 */
void AD7606B_Parallel_GPIO_Init(void) {

  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO Ports Clock Enable */
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);

  /*Configure GPIO pin Output Level */
  GPIO_WriteBit(GPIOC,
                AD7606B_DB9_Pin | AD7606B_DB8_Pin | AD7606B_DB7_Pin |
                    AD7606B_DB6_Pin | AD7606B_CONV_Pin | AD7606B_STBY_Pin |
                    AD7606B_SER_Pin | AD7606B_OSI2_Pin | AD7606B_OSI1_Pin |
                    AD7606B_OSI0_Pin | AD7606B_DB11_Pin | AD7606B_DB10_Pin |
                    AD7606B_DB13_Pin | AD7606B_DB12_Pin | AD7606B_REST_Pin |
                    AD7606B_WR_Pin,
                Bit_RESET);

  /*Configure GPIO pin Output Level */
  GPIO_WriteBit(GPIOA,
                AD7606B_DB3_Pin | AD7606B_DB2_Pin | AD7606B_DB1_Pin |
                    AD7606B_DB0_Pin | AD7606B_CS_Pin | AD7606B_RD_Pin |
                    AD7606B_DB15_Pin | AD7606B_DB14_Pin,
                Bit_RESET);

  /*Configure GPIO pin Output Level */
  GPIO_WriteBit(GPIOB, AD7606B_DB5_Pin | AD7606B_DB4_Pin, Bit_RESET);

  GPIO_InitStruct.GPIO_Pin =
      AD7606B_DB9_Pin | AD7606B_DB8_Pin | AD7606B_DB7_Pin | AD7606B_DB6_Pin |
      AD7606B_CONV_Pin | AD7606B_STBY_Pin | AD7606B_SER_Pin | AD7606B_OSI2_Pin |
      AD7606B_OSI1_Pin | AD7606B_OSI0_Pin | AD7606B_DB11_Pin |
      AD7606B_DB10_Pin | AD7606B_DB13_Pin | AD7606B_DB12_Pin |
      AD7606B_REST_Pin | AD7606B_WR_Pin;
  GPIO_InitStruct.GPIO_Mode = GPIO_Mode_Out_PP;
  GPIO_InitStruct.GPIO_Speed = GPIO_Speed_50MHz;
  GPIO_Init(GPIOC, &GPIO_InitStruct);

  GPIO_InitStruct.GPIO_Pin =
      AD7606B_DB3_Pin | AD7606B_DB2_Pin | AD7606B_DB1_Pin | AD7606B_DB0_Pin |
      AD7606B_CS_Pin | AD7606B_RD_Pin | AD7606B_DB15_Pin | AD7606B_DB14_Pin;
  GPIO_InitStruct.GPIO_Mode = GPIO_Mode_Out_PP;
  GPIO_InitStruct.GPIO_Speed = GPIO_Speed_50MHz;
  GPIO_Init(GPIOA, &GPIO_InitStruct);

  GPIO_InitStruct.GPIO_Pin = AD7606B_FRD_Pin | AD7606B_BUSY_Pin;
  GPIO_InitStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
  GPIO_Init(GPIOA, &GPIO_InitStruct);

  GPIO_InitStruct.GPIO_Pin = AD7606B_DB5_Pin | AD7606B_DB4_Pin;
  GPIO_InitStruct.GPIO_Mode = GPIO_Mode_Out_PP;
  GPIO_InitStruct.GPIO_Speed = GPIO_Speed_50MHz;
  GPIO_Init(GPIOB, &GPIO_InitStruct);
}

void AD7606B_Serial_GPIO_Init(void) {

  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO Ports Clock Enable */
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

  /*Configure GPIO pin Output Level */
  GPIO_WriteBit(GPIOC,
                AD7606B_OSI0_Pin | AD7606B_OSI1_Pin | AD7606B_OSI2_Pin |
                    AD7606B_SER_Pin | AD7606B_STBY_Pin | AD7606B_CONV_Pin |
                    AD7606B_REST_Pin | AD7606B_DB11_Pin,
                Bit_RESET);

  /*Configure GPIO pin Output Level */
  GPIO_WriteBit(GPIOA, AD7606B_CS_Pin | AD7606B_RD_Pin, Bit_RESET);

  GPIO_InitStruct.GPIO_Pin =
      AD7606B_OSI0_Pin | AD7606B_OSI1_Pin | AD7606B_OSI2_Pin | AD7606B_SER_Pin |
      AD7606B_STBY_Pin | AD7606B_CONV_Pin | AD7606B_REST_Pin | AD7606B_DB11_Pin;
  GPIO_InitStruct.GPIO_Mode = GPIO_Mode_Out_PP;
  GPIO_InitStruct.GPIO_Speed = GPIO_Speed_50MHz;
  GPIO_Init(GPIOC, &GPIO_InitStruct);

  GPIO_InitStruct.GPIO_Pin = AD7606B_CS_Pin | AD7606B_RD_Pin;
  GPIO_InitStruct.GPIO_Mode = GPIO_Mode_Out_PP;
  GPIO_InitStruct.GPIO_Speed = GPIO_Speed_50MHz;
  GPIO_Init(GPIOA, &GPIO_InitStruct);

  GPIO_InitStruct.GPIO_Pin = AD7606B_FRD_Pin | AD7606B_BUSY_Pin;
  GPIO_InitStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
  GPIO_Init(GPIOA, &GPIO_InitStruct);
}
/* USER CODE END 2 */
