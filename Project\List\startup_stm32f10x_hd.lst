


ARM Macro Assembler    Page 1 


    1 00000000         ;******************** (C) COPYRIGHT 2011 STMicroelectron
                       ics ********************
    2 00000000         ;* File Name          : startup_stm32f10x_hd.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Version            : V3.5.0
    5 00000000         ;* Date               : 11-March-2011
    6 00000000         ;* Description        : STM32F10x High Density Devices v
                       ector table for MDK-ARM 
    7 00000000         ;*                      toolchain. 
    8 00000000         ;*                      This module performs:
    9 00000000         ;*                      - Set the initial SP
   10 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
   11 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
   12 00000000         ;*                      - Configure the clock system and
                        also configure the external 
   13 00000000         ;*                        SRAM mounted on STM3210E-EVAL 
                       board to be used as data 
   14 00000000         ;*                        memory (optional, to be enable
                       d by user)
   15 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   16 00000000         ;*                        calls main()).
   17 00000000         ;*                      After Reset the CortexM3 process
                       or is in Thread mode,
   18 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   19 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>   
   20 00000000         ;*******************************************************
                       ************************
   21 00000000         ; THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS A
                       T PROVIDING CUSTOMERS
   22 00000000         ; WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN OR
                       DER FOR THEM TO SAVE TIME.
   23 00000000         ; AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIAB
                       LE FOR ANY DIRECT,
   24 00000000         ; INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY 
                       CLAIMS ARISING FROM THE
   25 00000000         ; CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOM
                       ERS OF THE CODING
   26 00000000         ; INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR 
                       PRODUCTS.
   27 00000000         ;*******************************************************
                       ************************
   28 00000000         
   29 00000000         ; Amount of memory (in bytes) allocated for Stack
   30 00000000         ; Tailor this value to your application needs
   31 00000000         ; <h> Stack Configuration
   32 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   33 00000000         ; </h>
   34 00000000         
   35 00000000 00000400 
                       Stack_Size
                               EQU              0x00000400
   36 00000000         
   37 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   38 00000000         Stack_Mem



ARM Macro Assembler    Page 2 


                               SPACE            Stack_Size
   39 00000400         __initial_sp
   40 00000400         
   41 00000400         ; <h> Heap Configuration
   42 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   43 00000400         ; </h>
   44 00000400         
   45 00000400 00000200 
                       Heap_Size
                               EQU              0x00000200
   46 00000400         
   47 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   48 00000000         __heap_base
   49 00000000         Heap_Mem
                               SPACE            Heap_Size
   50 00000200         __heap_limit
   51 00000200         
   52 00000200                 PRESERVE8
   53 00000200                 THUMB
   54 00000200         
   55 00000200         
   56 00000200         ; Vector Table Mapped to Address 0 at Reset
   57 00000200                 AREA             RESET, DATA, READONLY
   58 00000000                 EXPORT           __Vectors
   59 00000000                 EXPORT           __Vectors_End
   60 00000000                 EXPORT           __Vectors_Size
   61 00000000         
   62 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   63 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   64 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   65 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   66 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   67 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   68 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   69 0000001C 00000000        DCD              0           ; Reserved
   70 00000020 00000000        DCD              0           ; Reserved
   71 00000024 00000000        DCD              0           ; Reserved
   72 00000028 00000000        DCD              0           ; Reserved
   73 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   74 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   75 00000034 00000000        DCD              0           ; Reserved
   76 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   77 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   78 00000040         
   79 00000040         ; External Interrupts
   80 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window Watchdog



ARM Macro Assembler    Page 3 


   81 00000044 00000000        DCD              PVD_IRQHandler ; PVD through EX
                                                            TI Line detect
   82 00000048 00000000        DCD              TAMPER_IRQHandler ; Tamper
   83 0000004C 00000000        DCD              RTC_IRQHandler ; RTC
   84 00000050 00000000        DCD              FLASH_IRQHandler ; Flash
   85 00000054 00000000        DCD              RCC_IRQHandler ; RCC
   86 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line 0
   87 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line 1
   88 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line 2
   89 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line 3
   90 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line 4
   91 0000006C 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   92 00000070 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; DMA1 Channel 2
   93 00000074 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; DMA1 Channel 3
   94 00000078 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; DMA1 Channel 4
   95 0000007C 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; DMA1 Channel 5
   96 00000080 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; DMA1 Channel 6
   97 00000084 00000000        DCD              DMA1_Channel7_IRQHandler 
                                                            ; DMA1 Channel 7
   98 00000088 00000000        DCD              ADC1_2_IRQHandler ; ADC1 & ADC2
                                                            
   99 0000008C 00000000        DCD              USB_HP_CAN1_TX_IRQHandler ; USB
                                                             High Priority or C
                                                            AN1 TX
  100 00000090 00000000        DCD              USB_LP_CAN1_RX0_IRQHandler ; US
                                                            B Low  Priority or 
                                                            CAN1 RX0
  101 00000094 00000000        DCD              CAN1_RX1_IRQHandler ; CAN1 RX1
  102 00000098 00000000        DCD              CAN1_SCE_IRQHandler ; CAN1 SCE
  103 0000009C 00000000        DCD              EXTI9_5_IRQHandler 
                                                            ; EXTI Line 9..5
  104 000000A0 00000000        DCD              TIM1_BRK_IRQHandler 
                                                            ; TIM1 Break
  105 000000A4 00000000        DCD              TIM1_UP_IRQHandler 
                                                            ; TIM1 Update
  106 000000A8 00000000        DCD              TIM1_TRG_COM_IRQHandler ; TIM1 
                                                            Trigger and Commuta
                                                            tion
  107 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare
  108 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2
  109 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3
  110 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4
  111 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                            
  112 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                            
  113 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                            
  114 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                            
  115 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1
  116 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2



ARM Macro Assembler    Page 4 


  117 000000D4 00000000        DCD              USART1_IRQHandler ; USART1
  118 000000D8 00000000        DCD              USART2_IRQHandler ; USART2
  119 000000DC 00000000        DCD              USART3_IRQHandler ; USART3
  120 000000E0 00000000        DCD              EXTI15_10_IRQHandler 
                                                            ; EXTI Line 15..10
  121 000000E4 00000000        DCD              RTCAlarm_IRQHandler ; RTC Alarm
                                                             through EXTI Line
  122 000000E8 00000000        DCD              USBWakeUp_IRQHandler ; USB Wake
                                                            up from suspend
  123 000000EC 00000000        DCD              TIM8_BRK_IRQHandler 
                                                            ; TIM8 Break
  124 000000F0 00000000        DCD              TIM8_UP_IRQHandler 
                                                            ; TIM8 Update
  125 000000F4 00000000        DCD              TIM8_TRG_COM_IRQHandler ; TIM8 
                                                            Trigger and Commuta
                                                            tion
  126 000000F8 00000000        DCD              TIM8_CC_IRQHandler ; TIM8 Captu
                                                            re Compare
  127 000000FC 00000000        DCD              ADC3_IRQHandler ; ADC3
  128 00000100 00000000        DCD              FSMC_IRQHandler ; FSMC
  129 00000104 00000000        DCD              SDIO_IRQHandler ; SDIO
  130 00000108 00000000        DCD              TIM5_IRQHandler ; TIM5
  131 0000010C 00000000        DCD              SPI3_IRQHandler ; SPI3
  132 00000110 00000000        DCD              UART4_IRQHandler ; UART4
  133 00000114 00000000        DCD              UART5_IRQHandler ; UART5
  134 00000118 00000000        DCD              TIM6_IRQHandler ; TIM6
  135 0000011C 00000000        DCD              TIM7_IRQHandler ; TIM7
  136 00000120 00000000        DCD              DMA2_Channel1_IRQHandler 
                                                            ; DMA2 Channel1
  137 00000124 00000000        DCD              DMA2_Channel2_IRQHandler 
                                                            ; DMA2 Channel2
  138 00000128 00000000        DCD              DMA2_Channel3_IRQHandler 
                                                            ; DMA2 Channel3
  139 0000012C 00000000        DCD              DMA2_Channel4_5_IRQHandler ; DM
                                                            A2 Channel4 & Chann
                                                            el5
  140 00000130         __Vectors_End
  141 00000130         
  142 00000130 00000130 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  143 00000130         
  144 00000130                 AREA             |.text|, CODE, READONLY
  145 00000000         
  146 00000000         ; Reset handler
  147 00000000         Reset_Handler
                               PROC
  148 00000000                 EXPORT           Reset_Handler             [WEAK
]
  149 00000000                 IMPORT           __main
  150 00000000                 IMPORT           SystemInit
  151 00000000 4806            LDR              R0, =SystemInit
  152 00000002 4780            BLX              R0
  153 00000004 4806            LDR              R0, =__main
  154 00000006 4700            BX               R0
  155 00000008                 ENDP
  156 00000008         
  157 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)



ARM Macro Assembler    Page 5 


  158 00000008         
  159 00000008         NMI_Handler
                               PROC
  160 00000008                 EXPORT           NMI_Handler                [WEA
K]
  161 00000008 E7FE            B                .
  162 0000000A                 ENDP
  164 0000000A         HardFault_Handler
                               PROC
  165 0000000A                 EXPORT           HardFault_Handler          [WEA
K]
  166 0000000A E7FE            B                .
  167 0000000C                 ENDP
  169 0000000C         MemManage_Handler
                               PROC
  170 0000000C                 EXPORT           MemManage_Handler          [WEA
K]
  171 0000000C E7FE            B                .
  172 0000000E                 ENDP
  174 0000000E         BusFault_Handler
                               PROC
  175 0000000E                 EXPORT           BusFault_Handler           [WEA
K]
  176 0000000E E7FE            B                .
  177 00000010                 ENDP
  179 00000010         UsageFault_Handler
                               PROC
  180 00000010                 EXPORT           UsageFault_Handler         [WEA
K]
  181 00000010 E7FE            B                .
  182 00000012                 ENDP
  183 00000012         SVC_Handler
                               PROC
  184 00000012                 EXPORT           SVC_Handler                [WEA
K]
  185 00000012 E7FE            B                .
  186 00000014                 ENDP
  188 00000014         DebugMon_Handler
                               PROC
  189 00000014                 EXPORT           DebugMon_Handler           [WEA
K]
  190 00000014 E7FE            B                .
  191 00000016                 ENDP
  192 00000016         PendSV_Handler
                               PROC
  193 00000016                 EXPORT           PendSV_Handler             [WEA
K]
  194 00000016 E7FE            B                .
  195 00000018                 ENDP
  196 00000018         SysTick_Handler
                               PROC
  197 00000018                 EXPORT           SysTick_Handler            [WEA
K]
  198 00000018 E7FE            B                .
  199 0000001A                 ENDP
  200 0000001A         
  201 0000001A         Default_Handler
                               PROC
  202 0000001A         



ARM Macro Assembler    Page 6 


  203 0000001A                 EXPORT           WWDG_IRQHandler            [WEA
K]
  204 0000001A                 EXPORT           PVD_IRQHandler             [WEA
K]
  205 0000001A                 EXPORT           TAMPER_IRQHandler          [WEA
K]
  206 0000001A                 EXPORT           RTC_IRQHandler             [WEA
K]
  207 0000001A                 EXPORT           FLASH_IRQHandler           [WEA
K]
  208 0000001A                 EXPORT           RCC_IRQHandler             [WEA
K]
  209 0000001A                 EXPORT           EXTI0_IRQHandler           [WEA
K]
  210 0000001A                 EXPORT           EXTI1_IRQHandler           [WEA
K]
  211 0000001A                 EXPORT           EXTI2_IRQHandler           [WEA
K]
  212 0000001A                 EXPORT           EXTI3_IRQHandler           [WEA
K]
  213 0000001A                 EXPORT           EXTI4_IRQHandler           [WEA
K]
  214 0000001A                 EXPORT           DMA1_Channel1_IRQHandler   [WEA
K]
  215 0000001A                 EXPORT           DMA1_Channel2_IRQHandler   [WEA
K]
  216 0000001A                 EXPORT           DMA1_Channel3_IRQHandler   [WEA
K]
  217 0000001A                 EXPORT           DMA1_Channel4_IRQHandler   [WEA
K]
  218 0000001A                 EXPORT           DMA1_Channel5_IRQHandler   [WEA
K]
  219 0000001A                 EXPORT           DMA1_Channel6_IRQHandler   [WEA
K]
  220 0000001A                 EXPORT           DMA1_Channel7_IRQHandler   [WEA
K]
  221 0000001A                 EXPORT           ADC1_2_IRQHandler          [WEA
K]
  222 0000001A                 EXPORT           USB_HP_CAN1_TX_IRQHandler  [WEA
K]
  223 0000001A                 EXPORT           USB_LP_CAN1_RX0_IRQHandler [WEA
K]
  224 0000001A                 EXPORT           CAN1_RX1_IRQHandler        [WEA
K]
  225 0000001A                 EXPORT           CAN1_SCE_IRQHandler        [WEA
K]
  226 0000001A                 EXPORT           EXTI9_5_IRQHandler         [WEA
K]
  227 0000001A                 EXPORT           TIM1_BRK_IRQHandler        [WEA
K]
  228 0000001A                 EXPORT           TIM1_UP_IRQHandler         [WEA
K]
  229 0000001A                 EXPORT           TIM1_TRG_COM_IRQHandler    [WEA
K]
  230 0000001A                 EXPORT           TIM1_CC_IRQHandler         [WEA
K]
  231 0000001A                 EXPORT           TIM2_IRQHandler            [WEA
K]
  232 0000001A                 EXPORT           TIM3_IRQHandler            [WEA



ARM Macro Assembler    Page 7 


K]
  233 0000001A                 EXPORT           TIM4_IRQHandler            [WEA
K]
  234 0000001A                 EXPORT           I2C1_EV_IRQHandler         [WEA
K]
  235 0000001A                 EXPORT           I2C1_ER_IRQHandler         [WEA
K]
  236 0000001A                 EXPORT           I2C2_EV_IRQHandler         [WEA
K]
  237 0000001A                 EXPORT           I2C2_ER_IRQHandler         [WEA
K]
  238 0000001A                 EXPORT           SPI1_IRQHandler            [WEA
K]
  239 0000001A                 EXPORT           SPI2_IRQHandler            [WEA
K]
  240 0000001A                 EXPORT           USART1_IRQHandler          [WEA
K]
  241 0000001A                 EXPORT           USART2_IRQHandler          [WEA
K]
  242 0000001A                 EXPORT           USART3_IRQHandler          [WEA
K]
  243 0000001A                 EXPORT           EXTI15_10_IRQHandler       [WEA
K]
  244 0000001A                 EXPORT           RTCAlarm_IRQHandler        [WEA
K]
  245 0000001A                 EXPORT           USBWakeUp_IRQHandler       [WEA
K]
  246 0000001A                 EXPORT           TIM8_BRK_IRQHandler        [WEA
K]
  247 0000001A                 EXPORT           TIM8_UP_IRQHandler         [WEA
K]
  248 0000001A                 EXPORT           TIM8_TRG_COM_IRQHandler    [WEA
K]
  249 0000001A                 EXPORT           TIM8_CC_IRQHandler         [WEA
K]
  250 0000001A                 EXPORT           ADC3_IRQHandler            [WEA
K]
  251 0000001A                 EXPORT           FSMC_IRQHandler            [WEA
K]
  252 0000001A                 EXPORT           SDIO_IRQHandler            [WEA
K]
  253 0000001A                 EXPORT           TIM5_IRQHandler            [WEA
K]
  254 0000001A                 EXPORT           SPI3_IRQHandler            [WEA
K]
  255 0000001A                 EXPORT           UART4_IRQHandler           [WEA
K]
  256 0000001A                 EXPORT           UART5_IRQHandler           [WEA
K]
  257 0000001A                 EXPORT           TIM6_IRQHandler            [WEA
K]
  258 0000001A                 EXPORT           TIM7_IRQHandler            [WEA
K]
  259 0000001A                 EXPORT           DMA2_Channel1_IRQHandler   [WEA
K]
  260 0000001A                 EXPORT           DMA2_Channel2_IRQHandler   [WEA
K]
  261 0000001A                 EXPORT           DMA2_Channel3_IRQHandler   [WEA
K]



ARM Macro Assembler    Page 8 


  262 0000001A                 EXPORT           DMA2_Channel4_5_IRQHandler [WEA
K]
  263 0000001A         
  264 0000001A         WWDG_IRQHandler
  265 0000001A         PVD_IRQHandler
  266 0000001A         TAMPER_IRQHandler
  267 0000001A         RTC_IRQHandler
  268 0000001A         FLASH_IRQHandler
  269 0000001A         RCC_IRQHandler
  270 0000001A         EXTI0_IRQHandler
  271 0000001A         EXTI1_IRQHandler
  272 0000001A         EXTI2_IRQHandler
  273 0000001A         EXTI3_IRQHandler
  274 0000001A         EXTI4_IRQHandler
  275 0000001A         DMA1_Channel1_IRQHandler
  276 0000001A         DMA1_Channel2_IRQHandler
  277 0000001A         DMA1_Channel3_IRQHandler
  278 0000001A         DMA1_Channel4_IRQHandler
  279 0000001A         DMA1_Channel5_IRQHandler
  280 0000001A         DMA1_Channel6_IRQHandler
  281 0000001A         DMA1_Channel7_IRQHandler
  282 0000001A         ADC1_2_IRQHandler
  283 0000001A         USB_HP_CAN1_TX_IRQHandler
  284 0000001A         USB_LP_CAN1_RX0_IRQHandler
  285 0000001A         CAN1_RX1_IRQHandler
  286 0000001A         CAN1_SCE_IRQHandler
  287 0000001A         EXTI9_5_IRQHandler
  288 0000001A         TIM1_BRK_IRQHandler
  289 0000001A         TIM1_UP_IRQHandler
  290 0000001A         TIM1_TRG_COM_IRQHandler
  291 0000001A         TIM1_CC_IRQHandler
  292 0000001A         TIM2_IRQHandler
  293 0000001A         TIM3_IRQHandler
  294 0000001A         TIM4_IRQHandler
  295 0000001A         I2C1_EV_IRQHandler
  296 0000001A         I2C1_ER_IRQHandler
  297 0000001A         I2C2_EV_IRQHandler
  298 0000001A         I2C2_ER_IRQHandler
  299 0000001A         SPI1_IRQHandler
  300 0000001A         SPI2_IRQHandler
  301 0000001A         USART1_IRQHandler
  302 0000001A         USART2_IRQHandler
  303 0000001A         USART3_IRQHandler
  304 0000001A         EXTI15_10_IRQHandler
  305 0000001A         RTCAlarm_IRQHandler
  306 0000001A         USBWakeUp_IRQHandler
  307 0000001A         TIM8_BRK_IRQHandler
  308 0000001A         TIM8_UP_IRQHandler
  309 0000001A         TIM8_TRG_COM_IRQHandler
  310 0000001A         TIM8_CC_IRQHandler
  311 0000001A         ADC3_IRQHandler
  312 0000001A         FSMC_IRQHandler
  313 0000001A         SDIO_IRQHandler
  314 0000001A         TIM5_IRQHandler
  315 0000001A         SPI3_IRQHandler
  316 0000001A         UART4_IRQHandler
  317 0000001A         UART5_IRQHandler
  318 0000001A         TIM6_IRQHandler
  319 0000001A         TIM7_IRQHandler



ARM Macro Assembler    Page 9 


  320 0000001A         DMA2_Channel1_IRQHandler
  321 0000001A         DMA2_Channel2_IRQHandler
  322 0000001A         DMA2_Channel3_IRQHandler
  323 0000001A         DMA2_Channel4_5_IRQHandler
  324 0000001A E7FE            B                .
  325 0000001C         
  326 0000001C                 ENDP
  327 0000001C         
  328 0000001C                 ALIGN
  329 0000001C         
  330 0000001C         ;*******************************************************
                       ************************
  331 0000001C         ; User Stack and Heap initialization
  332 0000001C         ;*******************************************************
                       ************************
  333 0000001C                 IF               :DEF:__MICROLIB
  334 0000001C         
  335 0000001C                 EXPORT           __initial_sp
  336 0000001C                 EXPORT           __heap_base
  337 0000001C                 EXPORT           __heap_limit
  338 0000001C         
  339 0000001C                 ELSE
  354                          ENDIF
  355 0000001C         
  356 0000001C                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M3 --apcs=interw
ork --depend=.\obj\startup_stm32f10x_hd.d -o.\obj\startup_stm32f10x_hd.o -I.\RT
E\_AD7606B_STM32F103_Parallel -IE:\Keil_v5\ARM\PACK\ARM\CMSIS\4.5.0\CMSIS\Inclu
de -IE:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.4.1\Device\Include --predefine="_
_MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 525" --predefine="_RTE_ S
ETA 1" --predefine="STM32F10X_HD SETA 1" --list=.\list\startup_stm32f10x_hd.lst
 ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 37 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 38 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 39 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      At line 62 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 335 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 47 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 49 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 48 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      At line 336 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
Comment: __heap_base used once
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 50 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      At line 337 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 57 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 62 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      At line 58 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 142 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

__Vectors_End 00000130

Symbol: __Vectors_End
   Definitions
      At line 140 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 59 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 142 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 144 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      None
Comment: .text unused
ADC1_2_IRQHandler 0000001A

Symbol: ADC1_2_IRQHandler
   Definitions
      At line 282 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 98 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 221 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

ADC3_IRQHandler 0000001A

Symbol: ADC3_IRQHandler
   Definitions
      At line 311 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 127 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 250 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 174 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 67 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 175 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

CAN1_RX1_IRQHandler 0000001A

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 285 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 101 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 224 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

CAN1_SCE_IRQHandler 0000001A




ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

Symbol: CAN1_SCE_IRQHandler
   Definitions
      At line 286 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 102 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 225 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA1_Channel1_IRQHandler 0000001A

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 275 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 91 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 214 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA1_Channel2_IRQHandler 0000001A

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 276 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 92 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 215 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA1_Channel3_IRQHandler 0000001A

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 277 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 93 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 216 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA1_Channel4_IRQHandler 0000001A

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 278 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 94 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 217 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA1_Channel5_IRQHandler 0000001A



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols


Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 279 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 95 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 218 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA1_Channel6_IRQHandler 0000001A

Symbol: DMA1_Channel6_IRQHandler
   Definitions
      At line 280 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 96 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 219 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA1_Channel7_IRQHandler 0000001A

Symbol: DMA1_Channel7_IRQHandler
   Definitions
      At line 281 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 97 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 220 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA2_Channel1_IRQHandler 0000001A

Symbol: DMA2_Channel1_IRQHandler
   Definitions
      At line 320 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 136 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 259 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA2_Channel2_IRQHandler 0000001A

Symbol: DMA2_Channel2_IRQHandler
   Definitions
      At line 321 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 137 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 260 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s




ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

DMA2_Channel3_IRQHandler 0000001A

Symbol: DMA2_Channel3_IRQHandler
   Definitions
      At line 322 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 138 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 261 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DMA2_Channel4_5_IRQHandler 0000001A

Symbol: DMA2_Channel4_5_IRQHandler
   Definitions
      At line 323 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 139 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 262 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 188 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 74 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 189 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 201 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 270 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 86 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 209 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

EXTI15_10_IRQHandler 0000001A




ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 304 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 120 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 243 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions
      At line 271 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 87 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 210 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 272 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 88 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 211 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions
      At line 273 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 89 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 212 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 274 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 90 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 213 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

EXTI9_5_IRQHandler 0000001A



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols


Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 287 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 103 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 226 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

FLASH_IRQHandler 0000001A

Symbol: FLASH_IRQHandler
   Definitions
      At line 268 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 84 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 207 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

FSMC_IRQHandler 0000001A

Symbol: FSMC_IRQHandler
   Definitions
      At line 312 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 128 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 251 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 164 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 65 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 165 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 296 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 112 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 235 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s




ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 295 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 111 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 234 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

I2C2_ER_IRQHandler 0000001A

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 298 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 114 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 237 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

I2C2_EV_IRQHandler 0000001A

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 297 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 113 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 236 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 169 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 66 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 170 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 159 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 64 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 160 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


PVD_IRQHandler 0000001A

Symbol: PVD_IRQHandler
   Definitions
      At line 265 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 81 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 204 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 192 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 76 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 193 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

RCC_IRQHandler 0000001A

Symbol: RCC_IRQHandler
   Definitions
      At line 269 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 85 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 208 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

RTCAlarm_IRQHandler 0000001A

Symbol: RTCAlarm_IRQHandler
   Definitions
      At line 305 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 121 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 244 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

RTC_IRQHandler 0000001A

Symbol: RTC_IRQHandler
   Definitions
      At line 267 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 83 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 206 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 147 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 63 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 148 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

SDIO_IRQHandler 0000001A

Symbol: SDIO_IRQHandler
   Definitions
      At line 313 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 129 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 252 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 299 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 115 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 238 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

SPI2_IRQHandler 0000001A

Symbol: SPI2_IRQHandler
   Definitions
      At line 300 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 116 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 239 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

SPI3_IRQHandler 0000001A

Symbol: SPI3_IRQHandler
   Definitions
      At line 315 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 131 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s



ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

      At line 254 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 183 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 73 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 184 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 196 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 77 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 197 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TAMPER_IRQHandler 0000001A

Symbol: TAMPER_IRQHandler
   Definitions
      At line 266 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 82 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 205 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM1_BRK_IRQHandler 0000001A

Symbol: TIM1_BRK_IRQHandler
   Definitions
      At line 288 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 104 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 227 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM1_CC_IRQHandler 0000001A

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 291 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 107 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

.s
      At line 230 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM1_TRG_COM_IRQHandler 0000001A

Symbol: TIM1_TRG_COM_IRQHandler
   Definitions
      At line 290 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 106 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 229 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM1_UP_IRQHandler 0000001A

Symbol: TIM1_UP_IRQHandler
   Definitions
      At line 289 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 105 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 228 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM2_IRQHandler 0000001A

Symbol: TIM2_IRQHandler
   Definitions
      At line 292 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 108 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 231 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM3_IRQHandler 0000001A

Symbol: TIM3_IRQHandler
   Definitions
      At line 293 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 109 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 232 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM4_IRQHandler 0000001A

Symbol: TIM4_IRQHandler
   Definitions
      At line 294 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

      At line 110 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 233 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM5_IRQHandler 0000001A

Symbol: TIM5_IRQHandler
   Definitions
      At line 314 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 130 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 253 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM6_IRQHandler 0000001A

Symbol: TIM6_IRQHandler
   Definitions
      At line 318 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 134 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 257 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM7_IRQHandler 0000001A

Symbol: TIM7_IRQHandler
   Definitions
      At line 319 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 135 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 258 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM8_BRK_IRQHandler 0000001A

Symbol: TIM8_BRK_IRQHandler
   Definitions
      At line 307 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 123 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 246 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM8_CC_IRQHandler 0000001A

Symbol: TIM8_CC_IRQHandler
   Definitions
      At line 310 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 126 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 249 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM8_TRG_COM_IRQHandler 0000001A

Symbol: TIM8_TRG_COM_IRQHandler
   Definitions
      At line 309 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 125 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 248 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

TIM8_UP_IRQHandler 0000001A

Symbol: TIM8_UP_IRQHandler
   Definitions
      At line 308 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 124 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 247 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler
   Definitions
      At line 316 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 132 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 255 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

UART5_IRQHandler 0000001A

Symbol: UART5_IRQHandler
   Definitions
      At line 317 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 133 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 256 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 301 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

.s
   Uses
      At line 117 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 240 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 302 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 118 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 241 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

USART3_IRQHandler 0000001A

Symbol: USART3_IRQHandler
   Definitions
      At line 303 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 119 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 242 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

USBWakeUp_IRQHandler 0000001A

Symbol: USBWakeUp_IRQHandler
   Definitions
      At line 306 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 122 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 245 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

USB_HP_CAN1_TX_IRQHandler 0000001A

Symbol: USB_HP_CAN1_TX_IRQHandler
   Definitions
      At line 283 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 99 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 222 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

USB_LP_CAN1_RX0_IRQHandler 0000001A

Symbol: USB_LP_CAN1_RX0_IRQHandler
   Definitions



ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols

      At line 284 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 100 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
      At line 223 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 179 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 68 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 180 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

WWDG_IRQHandler 0000001A

Symbol: WWDG_IRQHandler
   Definitions
      At line 264 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 80 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
      At line 203 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s

72 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 45 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      At line 49 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 35 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
   Uses
      At line 38 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
Comment: Stack_Size used once
__Vectors_Size 00000130

Symbol: __Vectors_Size
   Definitions
      At line 142 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 60 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.
s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 150 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 151 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 149 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
   Uses
      At line 153 in file ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd
.s
Comment: __main used once
2 symbols
423 symbols in table
