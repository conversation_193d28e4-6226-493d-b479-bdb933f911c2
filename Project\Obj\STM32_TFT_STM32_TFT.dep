Dependencies for Project 'STM32_TFT', Target 'STM32_TFT': (DO NOT MODIFY !)
F (..\User\main.c)(0x5D0B28A0)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\main.o --omf_browse .\obj\main.crf --depend .\obj\main.d)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
I (..\User\Delay.h)(0x5D0B23A6)
I (..\User\bmp.h)(0x52FD8CD8)
I (..\User\lze_lcd.h)(0x5D0B28E5)
I (E:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (..\User\Fonts\fonts.h)(0x5D0BEA2D)
I (..\User\PeripheralInit.h)(0x5D0B2891)
F (..\User\PeripheralInit.c)(0x5D0B288C)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\peripheralinit.o --omf_browse .\obj\peripheralinit.crf --depend .\obj\peripheralinit.d)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
I (..\User\Delay.h)(0x5D0B23A6)
I (..\User\lze_lcd.h)(0x5D0B28E5)
I (E:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (..\User\Fonts\fonts.h)(0x5D0BEA2D)
I (..\User\PeripheralInit.h)(0x5D0B2891)
F (..\User\Delay.c)(0x5D0B23A2)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\delay.o --omf_browse .\obj\delay.crf --depend .\obj\delay.d)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
I (..\User\Delay.h)(0x5D0B23A6)
F (..\User\lze_lcd.c)(0x5D0BECB6)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\lze_lcd.o --omf_browse .\obj\lze_lcd.crf --depend .\obj\lze_lcd.d)
I (..\User\lze_lcd.h)(0x5D0B28E5)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
I (E:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x569DEA3A)
I (..\User\Fonts\fonts.h)(0x5D0BEA2D)
I (..\User\Delay.h)(0x5D0B23A6)
I (..\User\./Fonts/font8.c)(0x5C0CD877)
I (..\User\./Fonts/font12.c)(0x5C0CD877)
I (..\User\./Fonts/font16.c)(0x5CDFC531)
I (..\User\./Fonts/font20.c)(0x5C0CD877)
I (..\User\./Fonts/font24.c)(0x5C0CD877)
I (..\User\./Fonts/GB1616.c)(0x5CEC05AA)
I (..\User\./Fonts/GB2424.c)(0x5CE4CB26)
I (..\User\./Fonts/GB3232.c)(0x5CE4D07E)
F (..\STM32F10x_StdPeriph_Driver\src\misc.c)(0x52FD8CDA)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\misc.o --omf_browse .\obj\misc.crf --depend .\obj\misc.d)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c)(0x52FD8CDA)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\stm32f10x_gpio.o --omf_browse .\obj\stm32f10x_gpio.crf --depend .\obj\stm32f10x_gpio.d)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c)(0x52FD8CDA)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\stm32f10x_rcc.o --omf_browse .\obj\stm32f10x_rcc.crf --depend .\obj\stm32f10x_rcc.d)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c)(0x52FD8CDA)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\stm32f10x_fsmc.o --omf_browse .\obj\stm32f10x_fsmc.crf --depend .\obj\stm32f10x_fsmc.d)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
F (..\CMSIS\CoreSupport\core_cm3.c)(0x52FD8CE4)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\core_cm3.o --omf_browse .\obj\core_cm3.crf --depend .\obj\core_cm3.d)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
F (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c)(0x52FD8CE2)(-c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\User -I ..\CMSIS -I ..\CMSIS\CoreSupport -I ..\CMSIS\DeviceSupport\STM32F10x -I ..\STM32F10x_StdPeriph_Driver\inc -I ..\User\Fonts

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

-D__UVISION_VERSION="525" -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o .\obj\system_stm32f10x.o --omf_browse .\obj\system_stm32f10x.crf --depend .\obj\system_stm32f10x.d)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\CoreSupport\core_cm3.h)(0x52FD8CE4)
I (E:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x569DEA3A)
I (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.h)(0x52FD8CE4)
I (..\CMSIS\DeviceSupport\STM32F10x\stm32f10x_conf.h)(0x52FD8CE2)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_fsmc.h)(0x52FD8CDA)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_gpio.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\stm32f10x_rcc.h)(0x52FD8CDC)
I (..\STM32F10x_StdPeriph_Driver\inc\misc.h)(0x52FD8CDA)
F (..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s)(0x52FD8CE4)(--cpu Cortex-M3 -g --apcs=interwork --pd "__MICROLIB SETA 1"

-IE:\Keil_v5\ARM\RV31\INC

-IE:\Keil_v5\ARM\CMSIS\Include

-IE:\Keil_v5\ARM\INC\ST\STM32F10x

--pd "__UVISION_VERSION SETA 525"

--list .\list\startup_stm32f10x_hd.lst --xref -o .\obj\startup_stm32f10x_hd.o --depend .\obj\startup_stm32f10x_hd.d)
