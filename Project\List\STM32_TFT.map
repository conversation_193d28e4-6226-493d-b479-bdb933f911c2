Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    main.o(i.main) refers to peripheralinit.o(i.PeripheralInit) for PeripheralInit
    main.o(i.main) refers to lze_lcd.o(i.LCD_Clear) for LCD_Clear
    main.o(i.main) refers to lze_lcd.o(i.LCD_Line_H) for LCD_Line_H
    main.o(i.main) refers to lze_lcd.o(i.LCD_Line_V) for LCD_Line_V
    main.o(i.main) refers to lze_lcd.o(i.LCD_DotLine_H) for LCD_DotLine_H
    main.o(i.main) refers to lze_lcd.o(i.LCD_DotLine_V) for LCD_DotLine_V
    main.o(i.main) refers to lze_lcd.o(i.LCD_SetPoint) for LCD_SetPoint
    main.o(i.main) refers to lze_lcd.o(i.LCD_WriteString) for LCD_WriteString
    main.o(i.main) refers to lze_lcd.o(i.LCD_WriteChinese24x24string) for LCD_WriteChinese24x24string
    main.o(i.main) refers to lze_lcd.o(i.LCD_WriteNumLong) for LCD_WriteNumLong
    main.o(i.main) refers to lze_lcd.o(i.LCD_WriteNumChar) for LCD_WriteNumChar
    main.o(i.main) refers to lze_lcd.o(i.LCD_WriteNumInt) for LCD_WriteNumInt
    main.o(i.main) refers to lze_lcd.o(i.LCD_LineBox) for LCD_LineBox
    main.o(i.main) refers to lze_lcd.o(i.LCD_FillBox) for LCD_FillBox
    main.o(i.main) refers to lze_lcd.o(i.LCD_DrawRgbPict) for LCD_DrawRgbPict
    main.o(i.main) refers to lze_lcd.o(.bss) for LCD_Dev
    main.o(i.main) refers to main.o(.data) for pic
    peripheralinit.o(i.PeripheralInit) refers to lze_lcd.o(i.LCD_Init) for LCD_Init
    lze_lcd.o(i.GetChineseCode) refers to lze_lcd.o(.data) for LCD_Currentfonts_CN
    lze_lcd.o(i.LCD_Clear) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_Clear) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_Clear) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    lze_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    lze_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    lze_lcd.o(i.LCD_DisplayChar) refers to lze_lcd.o(i.LCD_DrawChar) for LCD_DrawChar
    lze_lcd.o(i.LCD_DisplayChar) refers to lze_lcd.o(.data) for LCD_Currentfonts
    lze_lcd.o(i.LCD_DisplayChineseChar) refers to lze_lcd.o(i.GetChineseCode) for GetChineseCode
    lze_lcd.o(i.LCD_DisplayChineseChar) refers to lze_lcd.o(i.LCD_DrawChineseChar) for LCD_DrawChineseChar
    lze_lcd.o(i.LCD_DisplayOff) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_DisplayOff) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_DisplayOff) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DisplayOn) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_DisplayOn) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_DisplayOn) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DisplayString) refers to lze_lcd.o(i.LCD_DisplayChar) for LCD_DisplayChar
    lze_lcd.o(i.LCD_DisplayString) refers to lze_lcd.o(i.LCD_DisplayChineseChar) for LCD_DisplayChineseChar
    lze_lcd.o(i.LCD_DisplayString) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DisplayString) refers to lze_lcd.o(.data) for LCD_Currentfonts
    lze_lcd.o(i.LCD_Display_Dir) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DotLine_H) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_DotLine_H) refers to lze_lcd.o(i.LCD_DrawDotHLine) for LCD_DrawDotHLine
    lze_lcd.o(i.LCD_DotLine_V) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_DotLine_V) refers to lze_lcd.o(i.LCD_DrawDotVLine) for LCD_DrawDotVLine
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(.data) for LCD_Currentfonts
    lze_lcd.o(i.LCD_DrawChar) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(.data) for LCD_Currentfonts_CN
    lze_lcd.o(i.LCD_DrawChineseChar) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_DrawDotHLine) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_DrawDotVLine) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_DrawFullRect) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_DrawHLine) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_DrawMonoPict) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawRect) refers to lze_lcd.o(i.LCD_DrawHLine) for LCD_DrawHLine
    lze_lcd.o(i.LCD_DrawRect) refers to lze_lcd.o(i.LCD_DrawVLine) for LCD_DrawVLine
    lze_lcd.o(i.LCD_DrawRgbPict) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawRgbPict) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawRgbPict) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawRgbPict) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_DrawVLine) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_FSMCConfig) refers to stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    lze_lcd.o(i.LCD_FSMCConfig) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    lze_lcd.o(i.LCD_FSMCConfig) refers to stm32f10x_fsmc.o(i.FSMC_NORSRAMInit) for FSMC_NORSRAMInit
    lze_lcd.o(i.LCD_FSMCConfig) refers to stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd) for FSMC_NORSRAMCmd
    lze_lcd.o(i.LCD_FillBox) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_FillBox) refers to lze_lcd.o(i.LCD_DrawFullRect) for LCD_DrawFullRect
    lze_lcd.o(i.LCD_FillBox) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_GetColors) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_CtrlLinesConfig) for LCD_CtrlLinesConfig
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_FSMCConfig) for LCD_FSMCConfig
    lze_lcd.o(i.LCD_Init) refers to delay.o(i.Delay_1ms) for Delay_1ms
    lze_lcd.o(i.LCD_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    lze_lcd.o(i.LCD_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_ReadReg) for LCD_ReadReg
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_ReadRAM) for LCD_ReadRAM
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(i.LCD_SetFont) for LCD_SetFont
    lze_lcd.o(i.LCD_Init) refers to lze_lcd.o(.data) for Font16_CN
    lze_lcd.o(i.LCD_LineBox) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_LineBox) refers to lze_lcd.o(i.LCD_DrawRect) for LCD_DrawRect
    lze_lcd.o(i.LCD_LineBox) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_Line_H) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_Line_H) refers to lze_lcd.o(i.LCD_DrawHLine) for LCD_DrawHLine
    lze_lcd.o(i.LCD_Line_H) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_Line_V) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_Line_V) refers to lze_lcd.o(i.LCD_DrawVLine) for LCD_DrawVLine
    lze_lcd.o(i.LCD_Line_V) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_PowerOn) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_PowerOn) refers to delay.o(i.Delay_1ms) for Delay_1ms
    lze_lcd.o(i.LCD_PowerOn) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_SetBackColor) refers to lze_lcd.o(.data) for BackColor
    lze_lcd.o(i.LCD_SetColors) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_SetCursor) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_SetCursor) refers to lze_lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lze_lcd.o(i.LCD_SetCursor) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_SetCursor) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lze_lcd.o(i.LCD_SetDisplayWindow) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_SetFont) refers to lze_lcd.o(.data) for LCD_Currentfonts
    lze_lcd.o(i.LCD_SetPoint) refers to lze_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lze_lcd.o(i.LCD_SetPoint) refers to lze_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lze_lcd.o(i.LCD_SetPoint) refers to lze_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lze_lcd.o(i.LCD_SetPoint) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_SetTextColor) refers to lze_lcd.o(.data) for TextColor
    lze_lcd.o(i.LCD_WindowModeDisable) refers to lze_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lze_lcd.o(i.LCD_WindowModeDisable) refers to lze_lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lze_lcd.o(i.LCD_WriteChinese24x24string) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_WriteChinese24x24string) refers to lze_lcd.o(i.LCD_SetFont) for LCD_SetFont
    lze_lcd.o(i.LCD_WriteChinese24x24string) refers to lze_lcd.o(i.LCD_DisplayString) for LCD_DisplayString
    lze_lcd.o(i.LCD_WriteChinese24x24string) refers to lze_lcd.o(.data) for Font24_CN
    lze_lcd.o(i.LCD_WriteNumChar) refers to printf7.o(i.__0sprintf$7) for __2sprintf
    lze_lcd.o(i.LCD_WriteNumChar) refers to lze_lcd.o(i.LCD_WriteString) for LCD_WriteString
    lze_lcd.o(i.LCD_WriteNumInt) refers to printf7.o(i.__0sprintf$7) for __2sprintf
    lze_lcd.o(i.LCD_WriteNumInt) refers to lze_lcd.o(i.LCD_WriteString) for LCD_WriteString
    lze_lcd.o(i.LCD_WriteNumLong) refers to printf7.o(i.__0sprintf$7) for __2sprintf
    lze_lcd.o(i.LCD_WriteNumLong) refers to lze_lcd.o(i.LCD_WriteString) for LCD_WriteString
    lze_lcd.o(i.LCD_WriteRAM_Prepare) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(i.LCD_WriteString) refers to lze_lcd.o(i.LCD_SetColors) for LCD_SetColors
    lze_lcd.o(i.LCD_WriteString) refers to lze_lcd.o(i.LCD_DisplayString) for LCD_DisplayString
    lze_lcd.o(i.PutPixel) refers to lze_lcd.o(i.LCD_DrawHLine) for LCD_DrawHLine
    lze_lcd.o(i.PutPixel) refers to lze_lcd.o(.bss) for LCD_Dev
    lze_lcd.o(.data) refers to lze_lcd.o(.constdata) for Font8_Table
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing delay.o(i.Delay), (18 bytes).
    Removing delay.o(i.Delay_10us), (36 bytes).
    Removing delay.o(i.Delay_1us), (26 bytes).
    Removing delay.o(i.Delay_250us), (36 bytes).
    Removing delay.o(i.Delay_2us), (26 bytes).
    Removing delay.o(i.Delay_50ms), (40 bytes).
    Removing delay.o(i.Delay_5ms), (38 bytes).
    Removing delay.o(i.Delay_882us), (26 bytes).
    Removing delay.o(i.Delay_ns), (12 bytes).
    Removing delay.o(i.TimingDelay_Decrement), (2 bytes).
    Removing lze_lcd.o(i.LCD_DisplayOff), (36 bytes).
    Removing lze_lcd.o(i.LCD_DisplayOn), (40 bytes).
    Removing lze_lcd.o(i.LCD_DrawMonoPict), (124 bytes).
    Removing lze_lcd.o(i.LCD_GetColors), (24 bytes).
    Removing lze_lcd.o(i.LCD_PowerOn), (136 bytes).
    Removing lze_lcd.o(i.LCD_SetBackColor), (16 bytes).
    Removing lze_lcd.o(i.LCD_SetTextColor), (16 bytes).
    Removing lze_lcd.o(i.LCD_WindowModeDisable), (28 bytes).
    Removing lze_lcd.o(i.PutPixel), (56 bytes).
    Removing misc.o(i.NVIC_Init), (112 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (212 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rcc.o(.data), (20 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

97 unused section(s) (total 5680 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CMSIS\CoreSupport\core_cm3.c          0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\User\Delay.c                          0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\PeripheralInit.c                 0x00000000   Number         0  peripheralinit.o ABSOLUTE
    ..\User\lze_lcd.c                        0x00000000   Number         0  lze_lcd.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\\CMSIS\\CoreSupport\\core_cm3.c       0x00000000   Number         0  core_cm3.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000168   Section        0  uldiv.o(.text)
    .text                                    0x080001cc   Section       36  init.o(.text)
    .text                                    0x080001f0   Section        0  llshl.o(.text)
    .text                                    0x0800020e   Section        0  llushr.o(.text)
    .text                                    0x0800022e   Section        0  __dczerorl2.o(.text)
    i.Delay_1ms                              0x08000284   Section        0  delay.o(i.Delay_1ms)
    i.FSMC_NORSRAMCmd                        0x080002a8   Section        0  stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd)
    i.FSMC_NORSRAMInit                       0x080002dc   Section        0  stm32f10x_fsmc.o(i.FSMC_NORSRAMInit)
    i.GPIO_Init                              0x080003c2   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ResetBits                         0x080004d8   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080004dc   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GetChineseCode                         0x080004e0   Section        0  lze_lcd.o(i.GetChineseCode)
    i.LCD_Clear                              0x0800053c   Section        0  lze_lcd.o(i.LCD_Clear)
    i.LCD_CtrlLinesConfig                    0x08000578   Section        0  lze_lcd.o(i.LCD_CtrlLinesConfig)
    i.LCD_DisplayChar                        0x080005ec   Section        0  lze_lcd.o(i.LCD_DisplayChar)
    i.LCD_DisplayChineseChar                 0x08000630   Section        0  lze_lcd.o(i.LCD_DisplayChineseChar)
    i.LCD_DisplayString                      0x08000654   Section        0  lze_lcd.o(i.LCD_DisplayString)
    i.LCD_Display_Dir                        0x080006d0   Section        0  lze_lcd.o(i.LCD_Display_Dir)
    i.LCD_DotLine_H                          0x0800081c   Section        0  lze_lcd.o(i.LCD_DotLine_H)
    i.LCD_DotLine_V                          0x08000842   Section        0  lze_lcd.o(i.LCD_DotLine_V)
    i.LCD_DrawChar                           0x08000868   Section        0  lze_lcd.o(i.LCD_DrawChar)
    LCD_DrawChar                             0x08000869   Thumb Code   140  lze_lcd.o(i.LCD_DrawChar)
    i.LCD_DrawChineseChar                    0x08000904   Section        0  lze_lcd.o(i.LCD_DrawChineseChar)
    LCD_DrawChineseChar                      0x08000905   Thumb Code   164  lze_lcd.o(i.LCD_DrawChineseChar)
    i.LCD_DrawDotHLine                       0x080009b8   Section        0  lze_lcd.o(i.LCD_DrawDotHLine)
    i.LCD_DrawDotVLine                       0x08000a1c   Section        0  lze_lcd.o(i.LCD_DrawDotVLine)
    i.LCD_DrawFullRect                       0x08000a80   Section        0  lze_lcd.o(i.LCD_DrawFullRect)
    i.LCD_DrawHLine                          0x08000ad0   Section        0  lze_lcd.o(i.LCD_DrawHLine)
    i.LCD_DrawRect                           0x08000b18   Section        0  lze_lcd.o(i.LCD_DrawRect)
    i.LCD_DrawRgbPict                        0x08000b58   Section        0  lze_lcd.o(i.LCD_DrawRgbPict)
    i.LCD_DrawVLine                          0x08000bb4   Section        0  lze_lcd.o(i.LCD_DrawVLine)
    i.LCD_FSMCConfig                         0x08000bfc   Section        0  lze_lcd.o(i.LCD_FSMCConfig)
    i.LCD_FillBox                            0x08000c68   Section        0  lze_lcd.o(i.LCD_FillBox)
    i.LCD_Init                               0x08000c98   Section        0  lze_lcd.o(i.LCD_Init)
    i.LCD_LineBox                            0x0800112c   Section        0  lze_lcd.o(i.LCD_LineBox)
    i.LCD_Line_H                             0x0800115c   Section        0  lze_lcd.o(i.LCD_Line_H)
    i.LCD_Line_V                             0x08001184   Section        0  lze_lcd.o(i.LCD_Line_V)
    i.LCD_ReadRAM                            0x080011ac   Section        0  lze_lcd.o(i.LCD_ReadRAM)
    i.LCD_ReadReg                            0x080011b8   Section        0  lze_lcd.o(i.LCD_ReadReg)
    i.LCD_SetColors                          0x080011c8   Section        0  lze_lcd.o(i.LCD_SetColors)
    i.LCD_SetCursor                          0x080011e4   Section        0  lze_lcd.o(i.LCD_SetCursor)
    LCD_SetCursor                            0x080011e5   Thumb Code   100  lze_lcd.o(i.LCD_SetCursor)
    i.LCD_SetDisplayWindow                   0x0800124c   Section        0  lze_lcd.o(i.LCD_SetDisplayWindow)
    i.LCD_SetFont                            0x080013dc   Section        0  lze_lcd.o(i.LCD_SetFont)
    i.LCD_SetPoint                           0x080013f0   Section        0  lze_lcd.o(i.LCD_SetPoint)
    i.LCD_WR_DATA                            0x08001478   Section        0  lze_lcd.o(i.LCD_WR_DATA)
    i.LCD_WR_REG                             0x08001484   Section        0  lze_lcd.o(i.LCD_WR_REG)
    i.LCD_WriteChinese24x24string            0x08001490   Section        0  lze_lcd.o(i.LCD_WriteChinese24x24string)
    i.LCD_WriteNumChar                       0x080014d4   Section        0  lze_lcd.o(i.LCD_WriteNumChar)
    i.LCD_WriteNumInt                        0x08001508   Section        0  lze_lcd.o(i.LCD_WriteNumInt)
    i.LCD_WriteNumLong                       0x0800153c   Section        0  lze_lcd.o(i.LCD_WriteNumLong)
    i.LCD_WriteRAM                           0x08001574   Section        0  lze_lcd.o(i.LCD_WriteRAM)
    i.LCD_WriteRAM_Prepare                   0x08001580   Section        0  lze_lcd.o(i.LCD_WriteRAM_Prepare)
    i.LCD_WriteReg                           0x08001594   Section        0  lze_lcd.o(i.LCD_WriteReg)
    i.LCD_WriteString                        0x080015a4   Section        0  lze_lcd.o(i.LCD_WriteString)
    i.PeripheralInit                         0x080015ca   Section        0  peripheralinit.o(i.PeripheralInit)
    i.RCC_AHBPeriphClockCmd                  0x080015d4   Section        0  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080015f4   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.SetSysClock                            0x08001614   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001615   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x0800161c   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x0800161d   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SystemInit                             0x080016fc   Section        0  system_stm32f10x.o(i.SystemInit)
    i.__0sprintf$7                           0x0800175c   Section        0  printf7.o(i.__0sprintf$7)
    i.__scatterload_copy                     0x08001784   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08001792   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001794   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x080017a4   Section        0  printf7.o(i._printf_core)
    _printf_core                             0x080017a5   Thumb Code   814  printf7.o(i._printf_core)
    i._printf_post_padding                   0x08001ad8   Section        0  printf7.o(i._printf_post_padding)
    _printf_post_padding                     0x08001ad9   Thumb Code    36  printf7.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08001afc   Section        0  printf7.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08001afd   Thumb Code    46  printf7.o(i._printf_pre_padding)
    i._sputc                                 0x08001b2a   Section        0  printf7.o(i._sputc)
    _sputc                                   0x08001b2b   Thumb Code    10  printf7.o(i._sputc)
    i.main                                   0x08001b34   Section        0  main.o(i.main)
    .constdata                               0x08001e7c   Section    16270  lze_lcd.o(.constdata)
    .data                                    0x20000000   Section     5000  main.o(.data)
    .data                                    0x20001388   Section       76  lze_lcd.o(.data)
    .bss                                     0x200013d4   Section       16  lze_lcd.o(.bss)
    STACK                                    0x200013e8   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x0800014d   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x0800014f   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x08000151   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x08000153   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x08000155   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x08000157   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x08000159   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x0800015b   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x0800015d   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_uldivmod                         0x08000169   Thumb Code    98  uldiv.o(.text)
    __scatterload                            0x080001cd   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080001cd   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x080001f1   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080001f1   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800020f   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800020f   Thumb Code     0  llushr.o(.text)
    __decompress                             0x0800022f   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x0800022f   Thumb Code    86  __dczerorl2.o(.text)
    Delay_1ms                                0x08000285   Thumb Code    36  delay.o(i.Delay_1ms)
    FSMC_NORSRAMCmd                          0x080002a9   Thumb Code    46  stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd)
    FSMC_NORSRAMInit                         0x080002dd   Thumb Code   230  stm32f10x_fsmc.o(i.FSMC_NORSRAMInit)
    GPIO_Init                                0x080003c3   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ResetBits                           0x080004d9   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080004dd   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GetChineseCode                           0x080004e1   Thumb Code    88  lze_lcd.o(i.GetChineseCode)
    LCD_Clear                                0x0800053d   Thumb Code    50  lze_lcd.o(i.LCD_Clear)
    LCD_CtrlLinesConfig                      0x08000579   Thumb Code   106  lze_lcd.o(i.LCD_CtrlLinesConfig)
    LCD_DisplayChar                          0x080005ed   Thumb Code    62  lze_lcd.o(i.LCD_DisplayChar)
    LCD_DisplayChineseChar                   0x08000631   Thumb Code    34  lze_lcd.o(i.LCD_DisplayChineseChar)
    LCD_DisplayString                        0x08000655   Thumb Code   110  lze_lcd.o(i.LCD_DisplayString)
    LCD_Display_Dir                          0x080006d1   Thumb Code   328  lze_lcd.o(i.LCD_Display_Dir)
    LCD_DotLine_H                            0x0800081d   Thumb Code    38  lze_lcd.o(i.LCD_DotLine_H)
    LCD_DotLine_V                            0x08000843   Thumb Code    38  lze_lcd.o(i.LCD_DotLine_V)
    LCD_DrawDotHLine                         0x080009b9   Thumb Code    88  lze_lcd.o(i.LCD_DrawDotHLine)
    LCD_DrawDotVLine                         0x08000a1d   Thumb Code    88  lze_lcd.o(i.LCD_DrawDotVLine)
    LCD_DrawFullRect                         0x08000a81   Thumb Code    70  lze_lcd.o(i.LCD_DrawFullRect)
    LCD_DrawHLine                            0x08000ad1   Thumb Code    64  lze_lcd.o(i.LCD_DrawHLine)
    LCD_DrawRect                             0x08000b19   Thumb Code    64  lze_lcd.o(i.LCD_DrawRect)
    LCD_DrawRgbPict                          0x08000b59   Thumb Code    86  lze_lcd.o(i.LCD_DrawRgbPict)
    LCD_DrawVLine                            0x08000bb5   Thumb Code    64  lze_lcd.o(i.LCD_DrawVLine)
    LCD_FSMCConfig                           0x08000bfd   Thumb Code   108  lze_lcd.o(i.LCD_FSMCConfig)
    LCD_FillBox                              0x08000c69   Thumb Code    42  lze_lcd.o(i.LCD_FillBox)
    LCD_Init                                 0x08000c99   Thumb Code  1154  lze_lcd.o(i.LCD_Init)
    LCD_LineBox                              0x0800112d   Thumb Code    42  lze_lcd.o(i.LCD_LineBox)
    LCD_Line_H                               0x0800115d   Thumb Code    36  lze_lcd.o(i.LCD_Line_H)
    LCD_Line_V                               0x08001185   Thumb Code    36  lze_lcd.o(i.LCD_Line_V)
    LCD_ReadRAM                              0x080011ad   Thumb Code     6  lze_lcd.o(i.LCD_ReadRAM)
    LCD_ReadReg                              0x080011b9   Thumb Code    12  lze_lcd.o(i.LCD_ReadReg)
    LCD_SetColors                            0x080011c9   Thumb Code    20  lze_lcd.o(i.LCD_SetColors)
    LCD_SetDisplayWindow                     0x0800124d   Thumb Code   396  lze_lcd.o(i.LCD_SetDisplayWindow)
    LCD_SetFont                              0x080013dd   Thumb Code    10  lze_lcd.o(i.LCD_SetFont)
    LCD_SetPoint                             0x080013f1   Thumb Code   130  lze_lcd.o(i.LCD_SetPoint)
    LCD_WR_DATA                              0x08001479   Thumb Code     6  lze_lcd.o(i.LCD_WR_DATA)
    LCD_WR_REG                               0x08001485   Thumb Code     6  lze_lcd.o(i.LCD_WR_REG)
    LCD_WriteChinese24x24string              0x08001491   Thumb Code    54  lze_lcd.o(i.LCD_WriteChinese24x24string)
    LCD_WriteNumChar                         0x080014d5   Thumb Code    46  lze_lcd.o(i.LCD_WriteNumChar)
    LCD_WriteNumInt                          0x08001509   Thumb Code    46  lze_lcd.o(i.LCD_WriteNumInt)
    LCD_WriteNumLong                         0x0800153d   Thumb Code    46  lze_lcd.o(i.LCD_WriteNumLong)
    LCD_WriteRAM                             0x08001575   Thumb Code     6  lze_lcd.o(i.LCD_WriteRAM)
    LCD_WriteRAM_Prepare                     0x08001581   Thumb Code    10  lze_lcd.o(i.LCD_WriteRAM_Prepare)
    LCD_WriteReg                             0x08001595   Thumb Code    10  lze_lcd.o(i.LCD_WriteReg)
    LCD_WriteString                          0x080015a5   Thumb Code    38  lze_lcd.o(i.LCD_WriteString)
    PeripheralInit                           0x080015cb   Thumb Code     8  peripheralinit.o(i.PeripheralInit)
    RCC_AHBPeriphClockCmd                    0x080015d5   Thumb Code    26  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080015f5   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    SystemInit                               0x080016fd   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    __0sprintf$7                             0x0800175d   Thumb Code    34  printf7.o(i.__0sprintf$7)
    __1sprintf$7                             0x0800175d   Thumb Code     0  printf7.o(i.__0sprintf$7)
    __2sprintf                               0x0800175d   Thumb Code     0  printf7.o(i.__0sprintf$7)
    __scatterload_copy                       0x08001785   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08001793   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001795   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    main                                     0x08001b35   Thumb Code   698  main.o(i.main)
    Font8_Table                              0x08001e7c   Data         760  lze_lcd.o(.constdata)
    Font12_Table                             0x08002174   Data        1140  lze_lcd.o(.constdata)
    Font16_Table                             0x080025e8   Data        1520  lze_lcd.o(.constdata)
    Font20_Table                             0x08002bd8   Data        3800  lze_lcd.o(.constdata)
    Font24_Table                             0x08003ab0   Data        6840  lze_lcd.o(.constdata)
    codeGB_16                                0x08005568   Data        1690  lze_lcd.o(.constdata)
    codeGB_24                                0x08005c02   Data         520  lze_lcd.o(.constdata)
    Region$$Table$$Base                      0x08005e0c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08005e2c   Number         0  anon$$obj.o(Region$$Table)
    pic                                      0x20000000   Data        5000  main.o(.data)
    Font8                                    0x20001388   Data           8  lze_lcd.o(.data)
    Font12                                   0x20001390   Data           8  lze_lcd.o(.data)
    Font16                                   0x20001398   Data           8  lze_lcd.o(.data)
    Font20                                   0x200013a0   Data           8  lze_lcd.o(.data)
    Font24                                   0x200013a8   Data           8  lze_lcd.o(.data)
    Font16_CN                                0x200013b0   Data          12  lze_lcd.o(.data)
    Font24_CN                                0x200013bc   Data          12  lze_lcd.o(.data)
    LCD_Currentfonts                         0x200013c8   Data           4  lze_lcd.o(.data)
    LCD_Currentfonts_CN                      0x200013cc   Data           4  lze_lcd.o(.data)
    TextColor                                0x200013d0   Data           2  lze_lcd.o(.data)
    BackColor                                0x200013d2   Data           2  lze_lcd.o(.data)
    LCD_Dev                                  0x200013d4   Data          16  lze_lcd.o(.bss)
    __initial_sp                             0x200017e8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007200, Max: 0x00040000, ABSOLUTE, COMPRESSED[0x00006af0])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00005e2c, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO         1002    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         1007  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         1271    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         1274    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1276    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1278    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         1279    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1281    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1283    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         1272    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO         1003    .text               startup_stm32f10x_hd.o
    0x08000168   0x08000168   0x00000062   Code   RO         1290    .text               mc_w.l(uldiv.o)
    0x080001ca   0x080001ca   0x00000002   PAD
    0x080001cc   0x080001cc   0x00000024   Code   RO         1303    .text               mc_w.l(init.o)
    0x080001f0   0x080001f0   0x0000001e   Code   RO         1306    .text               mc_w.l(llshl.o)
    0x0800020e   0x0800020e   0x00000020   Code   RO         1308    .text               mc_w.l(llushr.o)
    0x0800022e   0x0800022e   0x00000056   Code   RO         1323    .text               mc_w.l(__dczerorl2.o)
    0x08000284   0x08000284   0x00000024   Code   RO           80    i.Delay_1ms         delay.o
    0x080002a8   0x080002a8   0x00000034   Code   RO          846    i.FSMC_NORSRAMCmd   stm32f10x_fsmc.o
    0x080002dc   0x080002dc   0x000000e6   Code   RO          848    i.FSMC_NORSRAMInit  stm32f10x_fsmc.o
    0x080003c2   0x080003c2   0x00000116   Code   RO          527    i.GPIO_Init         stm32f10x_gpio.o
    0x080004d8   0x080004d8   0x00000004   Code   RO          534    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080004dc   0x080004dc   0x00000004   Code   RO          535    i.GPIO_SetBits      stm32f10x_gpio.o
    0x080004e0   0x080004e0   0x0000005c   Code   RO          150    i.GetChineseCode    lze_lcd.o
    0x0800053c   0x0800053c   0x0000003c   Code   RO          151    i.LCD_Clear         lze_lcd.o
    0x08000578   0x08000578   0x00000074   Code   RO          152    i.LCD_CtrlLinesConfig  lze_lcd.o
    0x080005ec   0x080005ec   0x00000044   Code   RO          153    i.LCD_DisplayChar   lze_lcd.o
    0x08000630   0x08000630   0x00000022   Code   RO          154    i.LCD_DisplayChineseChar  lze_lcd.o
    0x08000652   0x08000652   0x00000002   PAD
    0x08000654   0x08000654   0x0000007c   Code   RO          157    i.LCD_DisplayString  lze_lcd.o
    0x080006d0   0x080006d0   0x0000014c   Code   RO          158    i.LCD_Display_Dir   lze_lcd.o
    0x0800081c   0x0800081c   0x00000026   Code   RO          159    i.LCD_DotLine_H     lze_lcd.o
    0x08000842   0x08000842   0x00000026   Code   RO          160    i.LCD_DotLine_V     lze_lcd.o
    0x08000868   0x08000868   0x0000009c   Code   RO          161    i.LCD_DrawChar      lze_lcd.o
    0x08000904   0x08000904   0x000000b4   Code   RO          162    i.LCD_DrawChineseChar  lze_lcd.o
    0x080009b8   0x080009b8   0x00000064   Code   RO          163    i.LCD_DrawDotHLine  lze_lcd.o
    0x08000a1c   0x08000a1c   0x00000064   Code   RO          164    i.LCD_DrawDotVLine  lze_lcd.o
    0x08000a80   0x08000a80   0x00000050   Code   RO          165    i.LCD_DrawFullRect  lze_lcd.o
    0x08000ad0   0x08000ad0   0x00000048   Code   RO          166    i.LCD_DrawHLine     lze_lcd.o
    0x08000b18   0x08000b18   0x00000040   Code   RO          168    i.LCD_DrawRect      lze_lcd.o
    0x08000b58   0x08000b58   0x0000005c   Code   RO          169    i.LCD_DrawRgbPict   lze_lcd.o
    0x08000bb4   0x08000bb4   0x00000048   Code   RO          170    i.LCD_DrawVLine     lze_lcd.o
    0x08000bfc   0x08000bfc   0x0000006c   Code   RO          171    i.LCD_FSMCConfig    lze_lcd.o
    0x08000c68   0x08000c68   0x00000030   Code   RO          172    i.LCD_FillBox       lze_lcd.o
    0x08000c98   0x08000c98   0x00000494   Code   RO          174    i.LCD_Init          lze_lcd.o
    0x0800112c   0x0800112c   0x00000030   Code   RO          175    i.LCD_LineBox       lze_lcd.o
    0x0800115c   0x0800115c   0x00000028   Code   RO          176    i.LCD_Line_H        lze_lcd.o
    0x08001184   0x08001184   0x00000028   Code   RO          177    i.LCD_Line_V        lze_lcd.o
    0x080011ac   0x080011ac   0x0000000c   Code   RO          179    i.LCD_ReadRAM       lze_lcd.o
    0x080011b8   0x080011b8   0x00000010   Code   RO          180    i.LCD_ReadReg       lze_lcd.o
    0x080011c8   0x080011c8   0x0000001c   Code   RO          182    i.LCD_SetColors     lze_lcd.o
    0x080011e4   0x080011e4   0x00000068   Code   RO          183    i.LCD_SetCursor     lze_lcd.o
    0x0800124c   0x0800124c   0x00000190   Code   RO          184    i.LCD_SetDisplayWindow  lze_lcd.o
    0x080013dc   0x080013dc   0x00000014   Code   RO          185    i.LCD_SetFont       lze_lcd.o
    0x080013f0   0x080013f0   0x00000088   Code   RO          186    i.LCD_SetPoint      lze_lcd.o
    0x08001478   0x08001478   0x0000000c   Code   RO          188    i.LCD_WR_DATA       lze_lcd.o
    0x08001484   0x08001484   0x0000000c   Code   RO          189    i.LCD_WR_REG        lze_lcd.o
    0x08001490   0x08001490   0x00000044   Code   RO          191    i.LCD_WriteChinese24x24string  lze_lcd.o
    0x080014d4   0x080014d4   0x00000034   Code   RO          192    i.LCD_WriteNumChar  lze_lcd.o
    0x08001508   0x08001508   0x00000034   Code   RO          193    i.LCD_WriteNumInt   lze_lcd.o
    0x0800153c   0x0800153c   0x00000038   Code   RO          194    i.LCD_WriteNumLong  lze_lcd.o
    0x08001574   0x08001574   0x0000000c   Code   RO          195    i.LCD_WriteRAM      lze_lcd.o
    0x08001580   0x08001580   0x00000014   Code   RO          196    i.LCD_WriteRAM_Prepare  lze_lcd.o
    0x08001594   0x08001594   0x00000010   Code   RO          197    i.LCD_WriteReg      lze_lcd.o
    0x080015a4   0x080015a4   0x00000026   Code   RO          198    i.LCD_WriteString   lze_lcd.o
    0x080015ca   0x080015ca   0x00000008   Code   RO           66    i.PeripheralInit    peripheralinit.o
    0x080015d2   0x080015d2   0x00000002   PAD
    0x080015d4   0x080015d4   0x00000020   Code   RO          636    i.RCC_AHBPeriphClockCmd  stm32f10x_rcc.o
    0x080015f4   0x080015f4   0x00000020   Code   RO          639    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001614   0x08001614   0x00000008   Code   RO          966    i.SetSysClock       system_stm32f10x.o
    0x0800161c   0x0800161c   0x000000e0   Code   RO          967    i.SetSysClockTo72   system_stm32f10x.o
    0x080016fc   0x080016fc   0x00000060   Code   RO          969    i.SystemInit        system_stm32f10x.o
    0x0800175c   0x0800175c   0x00000028   Code   RO         1193    i.__0sprintf$7      mc_w.l(printf7.o)
    0x08001784   0x08001784   0x0000000e   Code   RO         1317    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08001792   0x08001792   0x00000002   Code   RO         1318    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001794   0x08001794   0x0000000e   Code   RO         1319    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080017a2   0x080017a2   0x00000002   PAD
    0x080017a4   0x080017a4   0x00000334   Code   RO         1198    i._printf_core      mc_w.l(printf7.o)
    0x08001ad8   0x08001ad8   0x00000024   Code   RO         1199    i._printf_post_padding  mc_w.l(printf7.o)
    0x08001afc   0x08001afc   0x0000002e   Code   RO         1200    i._printf_pre_padding  mc_w.l(printf7.o)
    0x08001b2a   0x08001b2a   0x0000000a   Code   RO         1202    i._sputc            mc_w.l(printf7.o)
    0x08001b34   0x08001b34   0x00000348   Code   RO            1    i.main              main.o
    0x08001e7c   0x08001e7c   0x00003f8e   Data   RO          201    .constdata          lze_lcd.o
    0x08005e0a   0x08005e0a   0x00000002   PAD
    0x08005e0c   0x08005e0c   0x00000020   Data   RO         1315    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08005e2c, Size: 0x000017e8, Max: 0x0000c000, ABSOLUTE, COMPRESSED[0x00000cc4])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00001388   Data   RW            2    .data               main.o
    0x20001388   COMPRESSED   0x0000004c   Data   RW          202    .data               lze_lcd.o
    0x200013d4        -       0x00000010   Zero   RW          200    .bss                lze_lcd.o
    0x200013e4   COMPRESSED   0x00000004   PAD
    0x200013e8        -       0x00000400   Zero   RW         1000    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   core_cm3.o
        36          0          0          0          0        492   delay.o
      4328        294      16270         76         16      34993   lze_lcd.o
       840        142          0       5000          0     225093   main.o
         8          0          0          0          0        469   peripheralinit.o
        36          8        304          0       1024        856   startup_stm32f10x_hd.o
       282          6          0          0          0       1913   stm32f10x_fsmc.o
       286          0          0          0          0       3164   stm32f10x_gpio.o
        64         12          0          0          0       1201   stm32f10x_rcc.o
       328         28          0          0          0       2133   system_stm32f10x.o

    ----------------------------------------------------------------------
      6212        <USER>      <GROUP>       5076       1044     270346   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          2          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
       952         12          0          0          0        420   printf7.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1288         <USER>          <GROUP>          0          0        716   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1284         28          0          0          0        716   mc_w.l

    ----------------------------------------------------------------------
      1288         <USER>          <GROUP>          0          0        716   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7500        518      16608       5076       1044     268330   Grand Totals
      7500        518      16608       3268       1044     268330   ELF Image Totals (compressed)
      7500        518      16608       3268          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                24108 (  23.54kB)
    Total RW  Size (RW Data + ZI Data)              6120 (   5.98kB)
    Total ROM Size (Code + RO Data + RW Data)      27376 (  26.73kB)

==============================================================================

