/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file    gpio.h
 * @brief   This file contains all the function prototypes for
 *          the gpio.c file
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2024 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __GPIO_H__
#define __GPIO_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/

/* USER CODE BEGIN Includes */
#include "stm32f10x_gpio.h"
/* USER CODE END Includes */

/* USER CODE BEGIN Private defines */
#define AD7606B_DB9_Pin GPIO_Pin_13
#define AD7606B_DB9_GPIO_Port GPIOC
#define AD7606B_DB8_Pin GPIO_Pin_12
#define AD7606B_DB8_GPIO_Port GPIOC
#define AD7606B_DB3_Pin GPIO_Pin_7
#define AD7606B_DB3_GPIO_Port GPIOA
#define AD7606B_DB2_Pin GPIO_Pin_6
#define AD7606B_DB2_GPIO_Port GPIOA
#define AD7606B_DB1_Pin GPIO_Pin_5
#define AD7606B_DB1_GPIO_Port GPIOA
#define AD7606B_DB0_Pin GPIO_Pin_4
#define AD7606B_DB0_GPIO_Port GPIOA
#define AD7606B_FRD_Pin GPIO_Pin_3
#define AD7606B_FRD_GPIO_Port GPIOA
#define AD7606B_BUSY_Pin GPIO_Pin_2
#define AD7606B_BUSY_GPIO_Port GPIOA
#define AD7606B_CS_Pin GPIO_Pin_1
#define AD7606B_CS_GPIO_Port GPIOA
#define AD7606B_RD_Pin GPIO_Pin_0
#define AD7606B_RD_GPIO_Port GPIOA
#define AD7606B_DB7_Pin GPIO_Pin_15
#define AD7606B_DB7_GPIO_Port GPIOC
#define AD7606B_DB6_Pin GPIO_Pin_14
#define AD7606B_DB6_GPIO_Port GPIOC
#define AD7606B_DB15_Pin GPIO_Pin_15
#define AD7606B_DB15_GPIO_Port GPIOA
#define AD7606B_DB14_Pin GPIO_Pin_14
#define AD7606B_DB14_GPIO_Port GPIOA
#define AD7606B_CONV_Pin GPIO_Pin_5
#define AD7606B_CONV_GPIO_Port GPIOC
#define AD7606B_STBY_Pin GPIO_Pin_4
#define AD7606B_STBY_GPIO_Port GPIOC
#define AD7606B_SER_Pin GPIO_Pin_3
#define AD7606B_SER_GPIO_Port GPIOC
#define AD7606B_OSI2_Pin GPIO_Pin_2
#define AD7606B_OSI2_GPIO_Port GPIOC
#define AD7606B_OSI1_Pin GPIO_Pin_1
#define AD7606B_OSI1_GPIO_Port GPIOC
#define AD7606B_OSI0_Pin GPIO_Pin_0
#define AD7606B_OSI0_GPIO_Port GPIOC
#define AD7606B_DB5_Pin GPIO_Pin_9
#define AD7606B_DB5_GPIO_Port GPIOB
#define AD7606B_DB4_Pin GPIO_Pin_8
#define AD7606B_DB4_GPIO_Port GPIOB
#define AD7606B_DB11_Pin GPIO_Pin_11
#define AD7606B_DB11_GPIO_Port GPIOC
#define AD7606B_DB10_Pin GPIO_Pin_10
#define AD7606B_DB10_GPIO_Port GPIOC
#define AD7606B_DB13_Pin GPIO_Pin_9
#define AD7606B_DB13_GPIO_Port GPIOC
#define AD7606B_DB12_Pin GPIO_Pin_8
#define AD7606B_DB12_GPIO_Port GPIOC
#define AD7606B_REST_Pin GPIO_Pin_7
#define AD7606B_REST_GPIO_Port GPIOC
#define AD7606B_WR_Pin GPIO_Pin_6
#define AD7606B_WR_GPIO_Port GPIOC
/* USER CODE END Private defines */

void MX_GPIO_Init(void);

/* USER CODE BEGIN Prototypes */
void AD7606B_Serial_GPIO_Init(void);
void AD7606B_Parallel_GPIO_Init(void);
/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif
#endif /*__ GPIO_H__ */
